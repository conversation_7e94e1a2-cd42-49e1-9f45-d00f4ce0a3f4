#include <jni.h>
#include <string>
#include <vector>
#include <memory> // For std::shared_ptr, std::make_shared, std::unique_ptr
#include <atomic>
#include <cmath> // For std::fabs, fmodf, floor, std::cyl_bessel_i (potentially with C++17, but provide fallback)
#include <algorithm> // For std::clamp, std::min, std::transform, std::max
#include <map>   // For std::map (cache)
#include <list>  // For std::list (LRU list)
#include <android/log.h>
#include <unistd.h> // For lseek, read, close, usleep
#include <fcntl.h>  // For O_RDONLY, might not be needed if FD is already open
#include <cerrno>   // For errno
#include <cstring>  // For strerror
#include <iomanip>  // For std::setw, std::setfill
#include <sstream>  // For std::stringstream
#include <functional> // For std::hash
#include <sys/stat.h> // For mkdir
#include <cinttypes>
#include <pthread.h> // For pthread functions

// CRITICAL FIX: Include the proper AudioSample class instead of duplicate struct
#include "AudioSample.h"
#include <thread>
#include <chrono>

// Define M_PI if not already defined (common in cmath but not guaranteed by standard before C++20)
#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

// Sinc Interpolation Parameters - Optimized for scratch responsiveness
constexpr int NUM_TAPS = 10; // Reduced from 16 for 50% faster interpolation
constexpr int SUBDIVISION_STEPS = 64; // Keep high precision (will test reducing later)
constexpr double KAISER_BETA = 5.0; // Reduced from 6.0 for less ringing
#include <android/asset_manager_jni.h> // For AAssetManager_fromJava
#include <oboe/Oboe.h>
#include <oboe/Utilities.h> // For oboe::convertToText

#define DR_WAV_IMPLEMENTATION
#include "dr_wav.h"
#define DR_MP3_IMPLEMENTATION
#include "dr_mp3.h"

#define APP_TAG "ScratchEmulator"
#define DEBUG_VERBOSE_SCRATCH_LOGGING 0  // Set to 1 to enable verbose scratch logging, 0 to disable
#define DEBUG_BUTTON_LOGGING 1  // Set to 1 to enable button-related logging, 0 to disable

#define ALOGI(...) __android_log_print(ANDROID_LOG_INFO, APP_TAG, __VA_ARGS__)
#define ALOGE(...) __android_log_print(ANDROID_LOG_ERROR, APP_TAG, __VA_ARGS__)
#define ALOGW(...) __android_log_print(ANDROID_LOG_WARN, APP_TAG, __VA_ARGS__)
#define ALOGV(...) __android_log_print(ANDROID_LOG_VERBOSE, APP_TAG, __VA_ARGS__)

// Conditional verbose logging for scratch operations
#if DEBUG_VERBOSE_SCRATCH_LOGGING
#define ALOGV_SCRATCH(...) __android_log_print(ANDROID_LOG_VERBOSE, APP_TAG, __VA_ARGS__)
#else
#define ALOGV_SCRATCH(...) ((void)0)
#endif

// Conditional logging for button-related operations
#if DEBUG_BUTTON_LOGGING
#define ALOGI_BUTTON(...) __android_log_print(ANDROID_LOG_INFO, APP_TAG, __VA_ARGS__)
#define ALOGV_BUTTON(...) __android_log_print(ANDROID_LOG_VERBOSE, APP_TAG, __VA_ARGS__)
#else
#define ALOGI_BUTTON(...) ((void)0)
#define ALOGV_BUTTON(...) ((void)0)
#endif

class AudioEngine;
class VinylTracker; // Forward declaration

// CRITICAL FIX: REMOVE DUPLICATE AudioSample STRUCT
// This duplicate struct was causing massive position conflicts!
// Use the proper AudioSample class from AudioSample.h instead
/*
struct AudioSample {
    std::string filePath; // Can be asset path or URI string for user files
    std::vector<float> audioData;
    int32_t totalFrames = 0;
    int32_t channels = 0;
    uint32_t sampleRate = 0;
    std::atomic<bool> isPlaying{false};
    std::atomic<bool> loop{false};
    bool playOnceThenLoopSilently = false;
    bool playedOnce = false;
    std::atomic<float> preciseCurrentFrame{0.0f};
    AudioEngine* audioEnginePtr = nullptr; // Pointer to the parent AudioEngine instance
    std::atomic<bool> useEngineRateForPlayback_{false};

    // EXPONENTIAL FADER SMOOTHING: Per-sample analog-like volume behavior
    mutable float currentSmoothedVolume_ = 0.0f; // Current smoothed volume level
    static constexpr float FADER_SMOOTHING_FACTOR = 0.15f; // Analog response (0.1-0.3 range)

    // Sinc table
    static std::vector<std::vector<float>> sincTable;
    static bool sincTableInitialized;
private:
    void resampleDataTo(uint32_t targetSampleRate);
public:
    static void precalculateSincTable();
    static double bessel_i0_approx(double x);
    static double kaiserWindow(double n_rel, double N_total_taps, double beta);

    // Persistent Cache constants
    static const uint32_t PERSISTENT_CACHE_MAGIC_NUMBER = 0xDF5C047C; // "DJScratchCache" somewhat
    static const uint32_t PERSISTENT_CACHE_VERSION = 1;

    bool saveToPersistentCache(const std::string& cacheFilePath, uint64_t modificationTimestamp);
    bool loadFromPersistentCache(const std::string& cacheFilePath, uint64_t expectedModificationTimestamp, const std::string& originalFilePathHint);


    bool hasExtension(const std::string& path, const std::string& extension);
    bool tryLoadPath(AAssetManager* assetManager, const std::string& currentPathToTry);
    void load(AAssetManager* assetManager, const std::string& basePath, AudioEngine* engine, uint64_t modificationTimestamp); // For assets
    void loadFromMemory(const unsigned char* buffer, size_t bufferSize, const std::string& identifier, uint64_t modificationTimestamp); // For user files from FD

    void getAudio(float* outputBuffer, int32_t numOutputFrames, int32_t outputStreamChannels, float effectiveVolume);

    inline float getSampleAt(int32_t frameIndex, int channelIndex) const {
        if (audioData.empty() || totalFrames == 0) return 0.0f;

        int32_t effectiveFrameIndex = frameIndex;
        if (loop.load()) {
            if (totalFrames > 0) {
                effectiveFrameIndex = frameIndex % totalFrames;
                if (effectiveFrameIndex < 0) {
                    effectiveFrameIndex += totalFrames;
                }
            } else {
                effectiveFrameIndex = 0;
            }
        } else {
            effectiveFrameIndex = std::max(0, std::min(frameIndex, totalFrames - 1));
        }

        size_t actualIndex = static_cast<size_t>(effectiveFrameIndex) * channels + (channelIndex % channels);
        if (actualIndex < audioData.size()) {
            return audioData[actualIndex];
        }
        return 0.0f;
    }

};
*/

// CRITICAL FIX: Remove duplicate static member initializations
// These are already properly defined in AudioSample.cpp
/*
// Static member initialization
std::vector<std::vector<float>> AudioSample::sincTable;
bool AudioSample::sincTableInitialized = false;

// Static member definitions for cache constants
const uint32_t AudioSample::PERSISTENT_CACHE_MAGIC_NUMBER;
const uint32_t AudioSample::PERSISTENT_CACHE_VERSION;
*/

// Persistent Cache File Format:
// uint32_t magic_number
// uint32_t version
// uint64_t original_file_mod_timestamp
// uint32_t sampleRate
// int32_t channels
// int32_t totalFrames
// float audioData[totalFrames * channels]

// CRITICAL FIX: DISABLE DUPLICATE saveToPersistentCache METHOD
/*
bool AudioSample::saveToPersistentCache(const std::string& cacheFilePath, uint64_t modificationTimestamp) {
    if (audioData.empty() || totalFrames == 0 || channels == 0 || sampleRate == 0) {
        ALOGE("AudioSample::saveToPersistentCache: No valid audio data to save for %s.", filePath.c_str());
        return false;
    }
    if (cacheFilePath.empty()) {
        ALOGE("AudioSample::saveToPersistentCache: Cache file path is empty for %s.", filePath.c_str());
        return false;
    }

    FILE* outFile = fopen(cacheFilePath.c_str(), "wb");
    if (!outFile) {
        ALOGE("AudioSample::saveToPersistentCache: Failed to open cache file %s for writing. Error: %s", cacheFilePath.c_str(), strerror(errno));
        return false;
    }

    bool success = true;
    size_t written = 0;

    // Write header
    written = fwrite(&PERSISTENT_CACHE_MAGIC_NUMBER, sizeof(PERSISTENT_CACHE_MAGIC_NUMBER), 1, outFile);
    success &= (written == 1);
    written = fwrite(&PERSISTENT_CACHE_VERSION, sizeof(PERSISTENT_CACHE_VERSION), 1, outFile);
    success &= (written == 1);
    written = fwrite(&modificationTimestamp, sizeof(modificationTimestamp), 1, outFile);
    success &= (written == 1);
    written = fwrite(&sampleRate, sizeof(sampleRate), 1, outFile);
    success &= (written == 1);
    written = fwrite(&channels, sizeof(channels), 1, outFile);
    success &= (written == 1);
    written = fwrite(&totalFrames, sizeof(totalFrames), 1, outFile);
    success &= (written == 1);

    if (!success) {
        ALOGE("AudioSample::saveToPersistentCache: Failed to write header to %s.", cacheFilePath.c_str());
        fclose(outFile);
        remove(cacheFilePath.c_str()); // Attempt to delete partial file
        return false;
    }

    // Write audio data
    written = fwrite(audioData.data(), sizeof(float), audioData.size(), outFile);
    if (written != audioData.size()) {
        ALOGE("AudioSample::saveToPersistentCache: Failed to write complete audio data to %s (wrote %zu of %zu floats).", cacheFilePath.c_str(), written, audioData.size());
        success = false;
        fclose(outFile);
        remove(cacheFilePath.c_str()); // Attempt to delete partial file
        return false;
    }

    fclose(outFile);
    if (success) {
        // OPTIMIZED: Reduced cache logging
        // ALOGI("AudioSample::saveToPersistentCache: Successfully saved processed audio for %s to %s (ModTime: %" PRIu64 ")", filePath.c_str(), cacheFilePath.c_str(), modificationTimestamp);
    }
    return success;
}
*/

// CRITICAL FIX: DISABLE ALL DUPLICATE AudioSample METHOD IMPLEMENTATIONS
// These duplicate methods are still causing object conflicts with AudioSample.cpp
/*
bool AudioSample::loadFromPersistentCache(const std::string& cacheFilePath, uint64_t expectedModificationTimestamp, const std::string& originalFilePathHint) {
    if (cacheFilePath.empty()) {
        ALOGW("AudioSample::loadFromPersistentCache: Cache file path is empty for hint %s.", originalFilePathHint.c_str());
        return false;
    }

    FILE* inFile = fopen(cacheFilePath.c_str(), "rb");
    if (!inFile) {
        // OPTIMIZED: Reduced cache miss logging
        // ALOGI("AudioSample::loadFromPersistentCache: Cache file %s not found or cannot be opened (this is normal if not cached yet).", cacheFilePath.c_str());
        return false;
    }

    uint32_t magic = 0, version = 0;
    uint64_t modTime = 0;
    uint32_t sr = 0;
    int32_t ch = 0, frames = 0;
    bool success = true;
    size_t readItems = 0;

    readItems = fread(&magic, sizeof(magic), 1, inFile);
    success &= (readItems == 1 && magic == PERSISTENT_CACHE_MAGIC_NUMBER);
    if (!success) { ALOGW("AudioSample::loadFromPersistentCache: Magic number mismatch or read error for %s.", cacheFilePath.c_str()); fclose(inFile); return false; }

    readItems = fread(&version, sizeof(version), 1, inFile);
    success &= (readItems == 1 && version == PERSISTENT_CACHE_VERSION);
    if (!success) { ALOGW("AudioSample::loadFromPersistentCache: Version mismatch or read error for %s.", cacheFilePath.c_str()); fclose(inFile); return false; }

    readItems = fread(&modTime, sizeof(modTime), 1, inFile);
    success &= (readItems == 1);
    if (!success) { ALOGW("AudioSample::loadFromPersistentCache: ModTime read error for %s.", cacheFilePath.c_str()); fclose(inFile); return false; }

    if (modTime != expectedModificationTimestamp) {
        ALOGI("AudioSample::loadFromPersistentCache: Stale cache for %s. Expected ModTime: %" PRIu64 ", Cached ModTime: %" PRIu64 ".", cacheFilePath.c_str(), expectedModificationTimestamp, modTime);
        fclose(inFile);
        // Optionally delete the stale cache file here, but be careful if multiple threads/processes could access
        // remove(cacheFilePath.c_str());
        return false;
    }

    readItems = fread(&sr, sizeof(sr), 1, inFile);
    success &= (readItems == 1);
    readItems = fread(&ch, sizeof(ch), 1, inFile);
    success &= (readItems == 1);
    readItems = fread(&frames, sizeof(frames), 1, inFile);
    success &= (readItems == 1);

    if (!success || sr == 0 || ch == 0 || frames == 0) {
        ALOGE("AudioSample::loadFromPersistentCache: Invalid header data (SR/CH/Frames zero or read error) in %s.", cacheFilePath.c_str());
        fclose(inFile);
        return false;
    }

    this->audioData.resize(static_cast<size_t>(frames) * ch);
    readItems = fread(this->audioData.data(), sizeof(float), this->audioData.size(), inFile);
    if (readItems != this->audioData.size()) {
        ALOGE("AudioSample::loadFromPersistentCache: Failed to read complete audio data from %s (read %zu of %zu floats).", cacheFilePath.c_str(), readItems, this->audioData.size());
        this->audioData.clear(); // Clear partial data
        fclose(inFile);
        return false;
    }

    fclose(inFile);

    // Successfully loaded from cache
    this->filePath = originalFilePathHint; // Set the original file path/URI
    this->sampleRate = sr;
    this->channels = ch;
    this->totalFrames = frames;
    this->isPlaying.store(false); // Reset playback state
    this->preciseCurrentFrame.store(0.0f);
    this->playOnceThenLoopSilently = false;
    this->playedOnce = false;

    // OPTIMIZED: Reduced cache success logging
    // ALOGI("AudioSample::loadFromPersistentCache: Successfully loaded processed audio for %s from %s (ModTime: %" PRIu64 ")", this->filePath.c_str(), cacheFilePath.c_str(), modTime);
    return true;
}
*/


// CRITICAL FIX: DISABLE ALL DUPLICATE AudioSample STATIC METHOD IMPLEMENTATIONS
/*
double AudioSample::bessel_i0_approx(double x) {
    double ax = std::abs(x);
    if (ax < 3.75) {
        double y = x / 3.75;
        y *= y;
        return 1.0 + y * (3.5156229 + y * (3.0899424 + y * (1.2067492 + y * (0.2659732 + y * (0.0360768 + y * 0.0045813)))));
    } else {
        double y = 3.75 / ax;
        return (std::exp(ax) / std::sqrt(ax)) * (0.39894228 + y * (0.01328592 + y * (0.00225319 + y * (-0.00157565 + y * (0.00916281 + y * (-0.02057706 + y * (0.02635537 + y * (-0.01647633 + y * 0.00392377))))))));
    }
}

double AudioSample::kaiserWindow(double n_rel_to_center, double N_total_taps, double beta) {
    if (std::abs(n_rel_to_center) > (N_total_taps / 2.0 - 0.5) && N_total_taps > 1) {
        return 0.0;
    }
    double term_val_for_bessel_arg;
    if (N_total_taps <= 1) term_val_for_bessel_arg = 0.0;
    else term_val_for_bessel_arg = (2.0 * (n_rel_to_center + (N_total_taps/2.0 - 0.5)) / (N_total_taps - 1.0)) - 1.0;

    double val_inside_sqrt = 1.0 - term_val_for_bessel_arg * term_val_for_bessel_arg;
    if (val_inside_sqrt < 0) val_inside_sqrt = 0;

    return bessel_i0_approx(beta * std::sqrt(val_inside_sqrt)) / bessel_i0_approx(beta);
}


void AudioSample::precalculateSincTable() {
    if (sincTableInitialized) return;

    sincTable.resize(SUBDIVISION_STEPS, std::vector<float>(NUM_TAPS));
    // double I0_beta = bessel_i0_approx(KAISER_BETA); // Denominator for Kaiser window, not directly needed if kaiserWindow returns normalized

    for (int j = 0; j < SUBDIVISION_STEPS; ++j) {
        double fractionalOffset = static_cast<double>(j) / SUBDIVISION_STEPS;
        float sumCoeffs = 0.0f;

        for (int i = 0; i < NUM_TAPS; ++i) {
            double sincPoint = (static_cast<double>(i) - (NUM_TAPS / 2.0 - 1.0)) - fractionalOffset;
            double sincValue;
            if (std::abs(sincPoint) < 1e-9) {
                sincValue = 1.0;
            } else {
                sincValue = std::sin(M_PI * sincPoint) / (M_PI * sincPoint);
            }
            double kaiser_n_rel = static_cast<double>(i) - (NUM_TAPS - 1.0) / 2.0;
            double windowValue = kaiserWindow(kaiser_n_rel, NUM_TAPS, KAISER_BETA);

            sincTable[j][i] = static_cast<float>(sincValue * windowValue);
            sumCoeffs += sincTable[j][i];
        }

        if (std::abs(sumCoeffs) > 1e-6) {
            for (int i = 0; i < NUM_TAPS; ++i) {
                sincTable[j][i] /= sumCoeffs;
            }
        }
    }
    sincTableInitialized = true;
    ALOGI("Sinc table precalculated: %d steps, %d taps. Beta: %f", SUBDIVISION_STEPS, NUM_TAPS, KAISER_BETA);
}
*/


class AudioEngine : public oboe::AudioStreamCallback {
public:
    std::atomic<float> platterTargetPlaybackRate_{1.0f};

    const float MOVEMENT_THRESHOLD = 0.001f;
    float degreesPerFrameForUnityRate_ = 2.5f;

    AAssetManager* appAssetManager_ = nullptr; // Made public for JNI asset loading access    // Audio samples - made public for JNI access
    std::shared_ptr<AudioSample> activePlatterSample_;
    std::shared_ptr<AudioSample> activeMusicSample_;    // Physical platter speed limiting for realistic scratching
    static constexpr float MAX_PLATTER_SPEED = 1.5f;    // 1.5x max realistic speed for testing
    static constexpr float SPEED_LIMIT_SMOOTHING = 0.85f; // Smooth speed transitions
    float previousLimitedSpeed = 1.0f;                   // Track previous speed

    AudioEngine() : streamSampleRate_(0),
                    activePlatterSample_(nullptr),
                    activeMusicSample_(nullptr) { // appAssetManager_ initialized in init()
        ALOGI("AudioEngine default constructor.");
        
        // Initialize VinylTracker with audio sync
        vinylTracker_ = std::make_unique<VinylTracker>(this);
        
        currentPlatterSampleIndex_.store(0); // Note: This C++ index is for internal asset lists, Kotlin manages the true playlist index
        currentMusicTrackIndex_.store(0);    // Same as above
        platterFaderVolume_.store(0.0f);
        generalMusicVolume_.store(0.9f);
        isFingerDownOnPlatter_.store(false);
        platterTargetPlaybackRate_.store(1.0f);

        
        // EXPONENTIAL FADER SMOOTHING: Per-sample smoothing now handled at AudioSample level
        // Each AudioSample instance maintains its own smoothed volume state for true analog behavior

        // These are default C++ internal asset paths, mainly for initial load or fallback.
        // The actual playlist logic is driven by Kotlin.
        internalAssetPlatterSamplePaths_ = {"sounds/haahhh", "sounds/sample1", "sounds/sample2"};
        internalAssetMusicTrackPaths_    = {"tracks/trackA", "tracks/trackB"};

    }    ~AudioEngine() { ALOGI("AudioEngine destructor."); release(); }
    
    // Physical platter speed limiter for realistic scratching
    float limitPlatterSpeed(float requestedSpeed, bool isManualTouch = false) {
        // Clamp to realistic physical range (8x max like real turntables)
        float clampedSpeed = std::clamp(requestedSpeed, -MAX_PLATTER_SPEED, MAX_PLATTER_SPEED);
        
        // CRITICAL FIX: FASTER CONVERGENCE FOR MANUAL TOUCH INPUT!
        // Manual touch needs responsive tracking but still needs smoothing for audio quality
        // Use different smoothing factors for different input types
        float smoothingFactor;
        if (isManualTouch) {
            // Much faster convergence for finger input - responsive but still smooth
            smoothingFactor = 0.3f;  // Was 0.85f - now converges 3x faster!
            
            // DEBUG: Log manual touch rate with faster convergence
            static int manualTouchLogCounter = 0;
            if (manualTouchLogCounter++ % 10 == 0) {
                ALOGE("MANUAL_TOUCH_FAST: Requested=%.6f -> Clamped=%.6f -> Smoothed=%.6f (Factor=%.2f)", 
                      requestedSpeed, clampedSpeed, 
                      previousLimitedSpeed * smoothingFactor + clampedSpeed * (1.0f - smoothingFactor),
                      smoothingFactor);
            }
        } else {
            // Slower convergence for motor control - prevents mechanical jumps
            smoothingFactor = SPEED_LIMIT_SMOOTHING;  // 0.85f - original value
            
            // DEBUG: Log motor control smoothing
            static int motorLogCounter = 0;
            if (motorLogCounter++ % 5 == 0) {
                ALOGE("MOTOR_CONTROL_SMOOTH: Requested=%.6f -> Clamped=%.6f -> Smoothed=%.6f (Factor=%.2f)", 
                      requestedSpeed, clampedSpeed,
                      previousLimitedSpeed * smoothingFactor + clampedSpeed * (1.0f - smoothingFactor),
                      smoothingFactor);
            }
        }
        
        // Apply smoothing with appropriate factor
        float oldPreviousSpeed = previousLimitedSpeed;
        previousLimitedSpeed = previousLimitedSpeed * smoothingFactor + 
                               clampedSpeed * (1.0f - smoothingFactor);
        
        return previousLimitedSpeed;
    }
    
    bool init(AAssetManager* mgr);
    void release();
    oboe::Result startStream();
    oboe::Result stopStream();    // Modified loading methods for caching - implemented inline below


    void stopMusicTrackInternal(); // This is a direct action, keep it.

    void setPlatterFaderVolumeInternal(float volume);
    void setMusicMasterVolumeInternal(float volume);
    void scratchPlatterActiveInternal(bool isActiveTouch, float angleDeltaOrRateFromViewModel);
    void releasePlatterTouchInternal();

    void setDegreesPerFrameForUnityRateInternal(float degrees) {
        if (degrees > 0.0f) {
            degreesPerFrameForUnityRate_ = degrees;
            ALOGI("AudioEngine: degreesPerFrameForUnityRate_ set to %.4f", degreesPerFrameForUnityRate_);
        } else {
            ALOGE("AudioEngine: Invalid degreesPerFrameForUnityRate_ value: %.4f", degrees);
        }
    }

    // Add a method to test the VinylTracker integration (implementation after VinylTracker class definition)
    void testVinylTracker();

    void engineLoadUserPlatterSample(const std::string& uriString, int fd, long offset, long length, uint64_t modificationTimestamp) {
        std::lock_guard<std::mutex> lock(cacheMutex_);
        // Use uriString + modTimestamp for in-memory cache key to differentiate versions if needed,
        // though persistent cache handles staleness by modTimestamp directly.
        // For simplicity, using uriString as key for in-memory LRU, assuming it points to the latest version loaded.
        std::string inMemoryCacheKey = uriString; // Or: uriString + "_" + std::to_string(modificationTimestamp);

        if (platterSampleCache_.count(inMemoryCacheKey)) {
            auto cachedSample = platterSampleCache_[inMemoryCacheKey];
            // Potentially re-validate against modificationTimestamp if in-memory cache could be stale
            // relative to a newer file version not yet loaded. For now, assume it's fine.
            
            // ===== CRITICAL DEBUG: Track pointer changes that might cause corruption =====
            AudioSample* oldSamplePtr = activePlatterSample_.get();
            float oldPosition = activePlatterSample_ ? activePlatterSample_->preciseCurrentFrame.load() : 0.0f;
            
            activePlatterSample_ = cachedSample;
            updateLru(platterSampleLru_, inMemoryCacheKey); // updateLru itself is not locked, but called from locked section
            
            AudioSample* newSamplePtr = activePlatterSample_.get();
            float newPosition = activePlatterSample_ ? activePlatterSample_->preciseCurrentFrame.load() : 0.0f;
            
            ALOGI("CACHE_POINTER_CHANGE: %p -> %p, Position: %.2f -> %.2f", 
                  oldSamplePtr, newSamplePtr, oldPosition, newPosition);
            
            // ===== CRITICAL FIX: CACHE-AWARE STATE PRESERVATION =====
            // Only reset state if NOT currently in active use (no touch, no playback)
            bool isCurrentlyTouched = isFingerDownOnPlatter_.load();
            bool isCurrentlyPlaying = (activePlatterSample_ && activePlatterSample_->isPlaying.load());
            bool isActivelyInUse = isCurrentlyTouched || isCurrentlyPlaying;
            
            // ===== POSITION CORRUPTION DETECTION: Monitor cache operations =====
            float positionBeforeCache = activePlatterSample_ ? activePlatterSample_->preciseCurrentFrame.load() : 0.0f;
            
            ALOGI("CACHE_HIT_USER: Sample '%s' - Touch=%d, Playing=%d, ActiveUse=%d", 
                  uriString.c_str(), isCurrentlyTouched, isCurrentlyPlaying, isActivelyInUse);
            ALOGI("CACHE_HIT_BEFORE: Touch=%d, Playing=%d, Position=%.2f", 
                  isCurrentlyTouched, isCurrentlyPlaying, positionBeforeCache);
            
            if (!isActivelyInUse) {
                // Safe to reset - no active interaction
                activePlatterSample_->preciseCurrentFrame.store(0.0f);
                activePlatterSample_->isPlaying.store(true);
                activePlatterSample_->loop.store(true);
                activePlatterSample_->useEngineRateForPlayback_.store(true);
                activePlatterSample_->playOnceThenLoopSilently = false;
                activePlatterSample_->playedOnce = false;
                ALOGI("CACHE_HIT_USER: SAFE state reset - no active interaction");
                ALOGI("CACHE_HIT_AFTER: Position=%.2f, Delta=%.2f (RESET)", 0.0f, -positionBeforeCache);
            } else {
                // PRESERVE current state during active use - this prevents position corruption!
                float positionAfterCache = activePlatterSample_->preciseCurrentFrame.load();
                ALOGI("CACHE_HIT_USER: PRESERVED state during active use - preventing corruption!");
                ALOGI("CACHE_HIT_AFTER: Position=%.2f, Delta=%.2f (PRESERVED)", positionAfterCache, positionAfterCache - positionBeforeCache);
                
                // ===== CRITICAL DEBUG: Check if position changed despite preservation =====
                if (std::abs(positionAfterCache - positionBeforeCache) > 1.0f) {
                    ALOGE("CACHE_CORRUPTION_DETECTED: Position changed %.2f -> %.2f despite preservation logic!", 
                          positionBeforeCache, positionAfterCache);
                }
            }
            return;
        }
        // OPTIMIZED: Keep only essential file loading logs
        ALOGI("AudioEngine: Loading platter sample '%s'", uriString.c_str());

        if (fd < 0 || length <= 0) { ALOGE("AudioEngine: Invalid FD or length for platter sample %s", uriString.c_str()); return; }
        std::vector<unsigned char> buffer(length);
        // FD reading logic
        off_t lseek_result = lseek(fd, offset, SEEK_SET);
        if (lseek_result == -1) { ALOGE("AudioEngine: Failed to lseek for platter sample '%s'. Error: %s", uriString.c_str(), strerror(errno)); return; }
        ssize_t bytesRead = read(fd, buffer.data(), length);
        if (bytesRead <= 0) { ALOGE("AudioEngine: Failed to read or read 0 bytes for platter sample '%s'. Error: %s", uriString.c_str(), strerror(errno)); return; }
        if (bytesRead != length) { ALOGW("AudioEngine: Read fewer bytes (%zd) than expected (%ld) for platter sample '%s'.", bytesRead, length, uriString.c_str()); buffer.resize(bytesRead); }
        // OPTIMIZED: Reduced successful read logging
        // ALOGI("AudioEngine: Successfully read %zd bytes for platter sample '%s'.", bytesRead, uriString.c_str());

        auto newSample = std::make_shared<AudioSample>();
        newSample->audioEnginePtr = this;
        // Pass modificationTimestamp to loadFromMemory, which handles persistent caching
        newSample->loadFromMemory(buffer.data(), buffer.size(), uriString, modificationTimestamp);

        if (newSample->totalFrames > 0) {
            ALOGI("AudioEngine: User platter sample '%s' (ModTime: %" PRIu64 ") loaded/decoded successfully.", uriString.c_str(), modificationTimestamp);
            newSample->loop.store(true);
            newSample->playOnceThenLoopSilently = false;
            newSample->preciseCurrentFrame.store(0.0f);
            newSample->isPlaying.store(true);
            newSample->useEngineRateForPlayback_.store(true);
            activePlatterSample_ = newSample;
            manageCache(platterSampleCache_, platterSampleLru_, inMemoryCacheKey, newSample); // Add to in-memory LRU
        } else {
            ALOGE("AudioEngine: Failed to load/decode user platter sample '%s' (ModTime: %" PRIu64 ").", uriString.c_str(), modificationTimestamp);
        }
    }

    void engineLoadUserMusicTrack(const std::string& uriString, int fd, long offset, long length, uint64_t modificationTimestamp) {
        std::lock_guard<std::mutex> lock(cacheMutex_);
        std::string inMemoryCacheKey = uriString;

        if (musicTrackCache_.count(inMemoryCacheKey)) {
            activeMusicSample_ = musicTrackCache_[inMemoryCacheKey];
            updateLru(musicTrackLru_, inMemoryCacheKey); // Called from locked section
            ALOGI("AudioEngine: Music track '%s' (ModTime: %" PRIu64 ") found in IN-MEMORY cache.", uriString.c_str(), modificationTimestamp);
            if (activeMusicSample_) {
                activeMusicSample_->preciseCurrentFrame.store(0.0f);
                activeMusicSample_->isPlaying.store(true);
                activeMusicSample_->loop.store(false);
                activeMusicSample_->useEngineRateForPlayback_.store(false);
            }
            return;
        }
        ALOGI("AudioEngine: Music track '%s' (ModTime: %" PRIu64 ") not in IN-MEMORY cache. Attempting to load (FD or persistent cache).", uriString.c_str(), modificationTimestamp);

        if (fd < 0 || length <= 0) { ALOGE("AudioEngine: Invalid FD or length for music track %s", uriString.c_str()); return; }
        std::vector<unsigned char> buffer(length);
        // FD reading logic
        off_t lseek_result = lseek(fd, offset, SEEK_SET);
        if (lseek_result == -1) { ALOGE("AudioEngine: Failed to lseek for music track '%s'. Error: %s", uriString.c_str(), strerror(errno)); return; }
        ssize_t bytesRead = read(fd, buffer.data(), length);
        if (bytesRead <= 0) { ALOGE("AudioEngine: Failed to read or read 0 bytes for music track '%s'. Error: %s", uriString.c_str(), strerror(errno)); return; }
        if (bytesRead != length) { ALOGW("AudioEngine: Read fewer bytes (%zd) than expected (%ld) for music track '%s'.", bytesRead, length, uriString.c_str()); buffer.resize(bytesRead); }
        ALOGI("AudioEngine: Successfully read %zd bytes for music track '%s'.", bytesRead, uriString.c_str());

        auto newSample = std::make_shared<AudioSample>();
        newSample->audioEnginePtr = this;
        newSample->loadFromMemory(buffer.data(), buffer.size(), uriString, modificationTimestamp);

        if (newSample->totalFrames > 0) {
            ALOGI("AudioEngine: User music track '%s' (ModTime: %" PRIu64 ") loaded/decoded successfully.", uriString.c_str(), modificationTimestamp);
            newSample->loop.store(false);
            newSample->playOnceThenLoopSilently = false;
            newSample->preciseCurrentFrame.store(0.0f);
            newSample->isPlaying.store(true);
            newSample->useEngineRateForPlayback_.store(false);
            activeMusicSample_ = newSample;
            manageCache(musicTrackCache_, musicTrackLru_, inMemoryCacheKey, newSample);
        } else {
            ALOGE("AudioEngine: Failed to load/decode user music track '%s' (ModTime: %" PRIu64 ").", uriString.c_str(), modificationTimestamp);
        }
    }

    void engineLoadAssetPlatter(const std::string& assetPath, uint64_t appVersionCode) {
        std::lock_guard<std::mutex> lock(cacheMutex_);
        if (!appAssetManager_) { ALOGE("AudioEngine: Asset manager not available for loading %s.", assetPath.c_str()); return; }
        // Use assetPath + appVersionCode for in-memory cache key to ensure freshness if app updates.
        std::string inMemoryCacheKey = assetPath + "_" + std::to_string(appVersionCode);

        if (platterSampleCache_.count(inMemoryCacheKey)) {
            // ===== CRITICAL DEBUG: Track pointer changes that might cause corruption =====
            AudioSample* oldSamplePtr = activePlatterSample_.get();
            float oldPosition = activePlatterSample_ ? activePlatterSample_->preciseCurrentFrame.load() : 0.0f;
            
            activePlatterSample_ = platterSampleCache_[inMemoryCacheKey];
            updateLru(platterSampleLru_, inMemoryCacheKey); // Called from locked section
            
            AudioSample* newSamplePtr = activePlatterSample_.get();
            float newPosition = activePlatterSample_ ? activePlatterSample_->preciseCurrentFrame.load() : 0.0f;
            
            ALOGI("CACHE_POINTER_CHANGE: %p -> %p, Position: %.2f -> %.2f", 
                  oldSamplePtr, newSamplePtr, oldPosition, newPosition);
            
            // ===== CRITICAL FIX: CACHE-AWARE STATE PRESERVATION FOR ASSETS =====
            bool isCurrentlyTouched = isFingerDownOnPlatter_.load();
            bool isCurrentlyPlaying = (activePlatterSample_ && activePlatterSample_->isPlaying.load());
            bool isActivelyInUse = isCurrentlyTouched || isCurrentlyPlaying;
            
            // ===== POSITION CORRUPTION DETECTION: Monitor asset cache operations =====
            float positionBeforeCache = activePlatterSample_ ? activePlatterSample_->preciseCurrentFrame.load() : 0.0f;
            
            ALOGI("CACHE_HIT_ASSET: Sample '%s' - Touch=%d, Playing=%d, ActiveUse=%d", 
                  assetPath.c_str(), isCurrentlyTouched, isCurrentlyPlaying, isActivelyInUse);
            ALOGI("CACHE_HIT_BEFORE: Touch=%d, Playing=%d, Position=%.2f", 
                  isCurrentlyTouched, isCurrentlyPlaying, positionBeforeCache);
            
            if (!isActivelyInUse) {
                // Safe to reset - no active interaction
                activePlatterSample_->preciseCurrentFrame.store(0.0f);
                activePlatterSample_->isPlaying.store(true);
                activePlatterSample_->loop.store(true);
                activePlatterSample_->useEngineRateForPlayback_.store(true);
                activePlatterSample_->playOnceThenLoopSilently = false;
                activePlatterSample_->playedOnce = false;
                
                // ===== PERFECT VINYL SYNC: SETUP MASTER TICK SYSTEM =====
                setupTicksPerVinylRotation();
                ALOGI("CACHE_HIT_ASSET: SAFE state reset with tick system setup");
                ALOGI("CACHE_HIT_AFTER: Position=%.2f, Delta=%.2f (RESET)", 0.0f, -positionBeforeCache);
            } else {
                // PRESERVE current state during active use - this prevents position corruption!
                float positionAfterCache = activePlatterSample_->preciseCurrentFrame.load();
                ALOGI("CACHE_HIT_ASSET: PRESERVED state during active use - preventing corruption!");
                ALOGI("CACHE_HIT_AFTER: Position=%.2f, Delta=%.2f (PRESERVED)", positionAfterCache, positionAfterCache - positionBeforeCache);
                
                // ===== CRITICAL DEBUG: Check if position changed despite preservation =====
                if (std::abs(positionAfterCache - positionBeforeCache) > 1.0f) {
                    ALOGE("CACHE_CORRUPTION_DETECTED: Position changed %.2f -> %.2f despite preservation logic!", 
                          positionBeforeCache, positionAfterCache);
                }
            }
            return;
        }
        ALOGI("AudioEngine: Asset platter sample '%s' (AppVer: %" PRIu64 ") not in IN-MEMORY cache. Loading.", assetPath.c_str(), appVersionCode);
        auto newSample = std::make_shared<AudioSample>();
        newSample->audioEnginePtr = this;
        // Pass appVersionCode as modificationTimestamp for asset caching logic
        newSample->load(this->appAssetManager_, assetPath, this, appVersionCode);
        if (newSample->totalFrames > 0) {
            newSample->loop.store(true);
            newSample->playOnceThenLoopSilently = false;
            newSample->preciseCurrentFrame.store(0.0f);
            newSample->isPlaying.store(true);
            newSample->useEngineRateForPlayback_.store(true);
            activePlatterSample_ = newSample;
            
            // ===== PERFECT VINYL SYNC: SETUP MASTER TICK SYSTEM =====
            setupTicksPerVinylRotation();
            
            manageCache(platterSampleCache_, platterSampleLru_, inMemoryCacheKey, newSample);
        } else {
            ALOGE("AudioEngine: Failed to load asset platter sample '%s' (AppVer: %" PRIu64 ").", assetPath.c_str(), appVersionCode);
        }
    }

    void engineLoadAssetMusic(const std::string& assetPath, uint64_t appVersionCode) {
        std::lock_guard<std::mutex> lock(cacheMutex_);
        if (!appAssetManager_) { ALOGE("AudioEngine: Asset manager not available for loading %s.", assetPath.c_str()); return; }
        std::string inMemoryCacheKey = assetPath + "_" + std::to_string(appVersionCode);

        if (musicTrackCache_.count(inMemoryCacheKey)) {
            activeMusicSample_ = musicTrackCache_[inMemoryCacheKey];
            updateLru(musicTrackLru_, inMemoryCacheKey); // Called from locked section
            ALOGI("AudioEngine: Asset music track '%s' (AppVer: %" PRIu64 ") found in IN-MEMORY cache.", assetPath.c_str(), appVersionCode);
            if (activeMusicSample_) {
                activeMusicSample_->preciseCurrentFrame.store(0.0f);
                activeMusicSample_->isPlaying.store(true);
                activeMusicSample_->loop.store(false);
                activeMusicSample_->useEngineRateForPlayback_.store(false);
            }
            return;
        }
        ALOGI("AudioEngine: Asset music track '%s' (AppVer: %" PRIu64 ") not in IN-MEMORY cache. Loading.", assetPath.c_str(), appVersionCode);
        auto newSample = std::make_shared<AudioSample>();
        newSample->audioEnginePtr = this;
        newSample->load(this->appAssetManager_, assetPath, this, appVersionCode);
        if (newSample->totalFrames > 0) {
            newSample->loop.store(false);
            newSample->playOnceThenLoopSilently = false;
            newSample->preciseCurrentFrame.store(0.0f);
            newSample->isPlaying.store(true);
            newSample->useEngineRateForPlayback_.store(false);
            activeMusicSample_ = newSample;
            manageCache(musicTrackCache_, musicTrackLru_, inMemoryCacheKey, newSample);
        } else {
            ALOGE("AudioEngine: Failed to load asset music track '%s' (AppVer: %" PRIu64 ").", assetPath.c_str(), appVersionCode);
        }
    }

    // For intro, appVersionCode is used as the effective "modificationTimestamp"
    void enginePlayIntroAndLoopOnPlatter(const std::string& assetPath, uint64_t appVersionCode) {
        std::lock_guard<std::mutex> lock(cacheMutex_);
        if (!appAssetManager_) { ALOGE("AudioEngine: Asset manager not available for intro %s.", assetPath.c_str()); return; }
        ALOGI("AudioEngine: enginePlayIntroAndLoopOnPlatter with ASSET path: %s, AppVer: %" PRIu64, assetPath.c_str(), appVersionCode);
        std::string inMemoryCacheKey = assetPath + "_intro_" + std::to_string(appVersionCode);


        if (platterSampleCache_.count(inMemoryCacheKey)) { // Check in-memory cache first
            activePlatterSample_ = platterSampleCache_[inMemoryCacheKey];
            updateLru(platterSampleLru_, inMemoryCacheKey); // Called from locked section
            ALOGI("AudioEngine: Intro asset '%s' (AppVer: %" PRIu64 ") found in IN-MEMORY cache.", assetPath.c_str(), appVersionCode);
        } else {
            ALOGI("AudioEngine: Intro asset '%s' (AppVer: %" PRIu64 ") not in IN-MEMORY cache. Loading.", assetPath.c_str(), appVersionCode);
            auto newSample = std::make_shared<AudioSample>();
            newSample->audioEnginePtr = this;
            newSample->load(this->appAssetManager_, assetPath, this, appVersionCode); // Use appVersionCode for caching
            if (newSample->totalFrames > 0) {
                activePlatterSample_ = newSample;
                manageCache(platterSampleCache_, platterSampleLru_, inMemoryCacheKey, newSample); // Add to in-memory LRU
            } else {
                ALOGE("Failed to load intro ASSET sample from path: %s (AppVer: %" PRIu64 ")", assetPath.c_str(), appVersionCode);
                return; // Failed to load, cannot proceed
            }
        }

        if (activePlatterSample_ && activePlatterSample_->totalFrames > 0) {
            activePlatterSample_->playOnceThenLoopSilently = true;
            activePlatterSample_->playedOnce = false;
            activePlatterSample_->loop.store(false);
            activePlatterSample_->preciseCurrentFrame.store(0.0f);
            activePlatterSample_->isPlaying.store(true);
            activePlatterSample_->useEngineRateForPlayback_.store(false);
            platterTargetPlaybackRate_.store(1.0f);
            ALOGI("Intro ASSET sample '%s' set up. Will play once then loop silently.", activePlatterSample_->filePath.c_str());
        } else {
            ALOGE("Failed to set up intro ASSET sample from path: %s (already logged error or sample is null)", assetPath.c_str());
        }
    }


    oboe::DataCallbackResult onAudioReady(oboe::AudioStream* stream, void* audioData, int32_t numFrames) override;
    void onErrorBeforeClose(oboe::AudioStream *stream, oboe::Result error) override;
    void onErrorAfterClose(oboe::AudioStream *stream, oboe::Result error) override;
    
    // Public method for cache file path generation (needed by AudioSample)
    std::string getPersistentCacheFilePath(const std::string& originalPathOrUri, uint64_t modificationTimestamp);

    bool isPlatterTouched() const { return isFingerDownOnPlatter_.load(); }
    uint32_t getStreamSampleRate() const { return streamSampleRate_; }

// Make unique_ptr members for samples public for direct access from JNI asset loaders
// These are internal C++ lists of ASSET paths, primarily for fallback or initial setup.
// The main playlist logic (including user URIs) is now driven by Kotlin.
    std::vector<std::string> internalAssetPlatterSamplePaths_;
    std::vector<std::string> internalAssetMusicTrackPaths_;
    std::atomic<int> currentPlatterSampleIndex_;
    std::atomic<int> currentMusicTrackIndex_;
    std::string baseCachePath_; // For persistent cache

    // VinylTracker for 120Hz angle tracking - made public for JNI access
    std::unique_ptr<VinylTracker> vinylTracker_;
    
    // VinylTracker public wrapper methods for JNI access (implemented after VinylTracker definition)
    void startVinylTracking();
    void stopVinylTracking();
    float getCurrentVinylAngle();
    bool isVinylTracking();
    
    // Additional public JNI wrapper methods
    void loadAssetPlatterSample(const std::string& assetPath, uint64_t appVersionCode) {
        engineLoadAssetPlatter(assetPath, appVersionCode);
    }
    
    void setScratchPlatterActive(bool active, float velocity) {
        scratchPlatterActiveInternal(active, velocity);
    }

    float getPlatterAudioPosition() {
        if (activePlatterSample_) {
            return activePlatterSample_->preciseCurrentFrame.load();
        }
        return 0.0f;
    }

    float getPlatterTotalFrames() {
        if (activePlatterSample_) {
            return static_cast<float>(activePlatterSample_->totalFrames);
        }
        return 1.0f; // Avoid division by zero
    }

    float getUnwrappedVinylAngle() {
        if (vinylTracker_) {
            return vinylTracker_->getUnwrappedAngle();
        }
        return 0.0f;
    }

    float getTotalRotations() {
        if (vinylTracker_) {
            return vinylTracker_->getTotalRotations();
        }
        return 0.0f;
    }

    void resetVinylAngle() {
        if (vinylTracker_) {
            vinylTracker_->resetAngle();
        }
    }

    // ===== PERFECT VINYL SYNC: MASTER TICK SYSTEM =====
    // Master tick system for precise timing - single source of truth
    class MasterTickSystem {
    private:
        std::atomic<uint64_t> masterTick_{0};
        std::atomic<uint32_t> sampleRate_{48000};
        std::atomic<uint64_t> ticksPerVinylRotation_{0}; // Will be set based on sample length

    public:
        MasterTickSystem() = default;

        // Called from audio callback - most precise timing source
        void incrementTick(uint32_t numFrames = 1) {
            masterTick_.fetch_add(numFrames);
        }

        uint64_t getCurrentTick() const {
            return masterTick_.load();
        }

        void setSampleRate(uint32_t rate) {
            sampleRate_.store(rate);
        }

        uint32_t getSampleRate() const {
            return sampleRate_.load();
        }

        void setTicksPerVinylRotation(uint64_t ticks) {
            ticksPerVinylRotation_.store(ticks);
        }

        uint64_t getTicksPerVinylRotation() const {
            return ticksPerVinylRotation_.load();
        }

        // Convert ticks to vinyl angle (0-360 degrees) - PRECISION FIX
        float ticksToVinylAngle(uint64_t ticks) const {
            uint64_t ticksPerRotation = ticksPerVinylRotation_.load();
            if (ticksPerRotation == 0) return 0.0f;

            uint64_t ticksInRotation = ticks % ticksPerRotation;
            // Use double precision to prevent integer overflow and precision loss
            return (float)((double)ticksInRotation * 360.0 / (double)ticksPerRotation);
        }

        // Convert ticks to unwrapped angle (can exceed 360) - PRECISION FIX
        float ticksToUnwrappedAngle(uint64_t ticks) const {
            uint64_t ticksPerRotation = ticksPerVinylRotation_.load();
            if (ticksPerRotation == 0) return 0.0f;

            // Use double precision to prevent integer overflow and precision loss
            return (float)((double)ticks * 360.0 / (double)ticksPerRotation);
        }

        void reset() {
            masterTick_.store(0);
        }
    };

    MasterTickSystem masterTickSystem_;
    
    // Master tick system access methods
    MasterTickSystem* getMasterTickSystem() { return &masterTickSystem_; }
    uint64_t getCurrentMasterTick() const { return masterTickSystem_.getCurrentTick(); }
    float getCurrentTickBasedVinylAngle() const;
    void resetMasterTicks();
    void setupTicksPerVinylRotation();

private:
    std::shared_ptr<oboe::AudioStream> audioStream_;
    uint32_t streamSampleRate_ = 0;
    std::atomic<float> platterFaderVolume_;
    std::atomic<float> generalMusicVolume_;
    std::atomic<bool> isFingerDownOnPlatter_;
    
    // EXPONENTIAL FADER SMOOTHING: Per-sample volume control moved to AudioSample level
    // This provides true analog-like behavior with instant response to big moves
    // and smooth micro-adjustments on a per-sample basis
    // Cache members
    static const size_t MAX_CACHED_SAMPLES_PER_TYPE = 2; // In-memory LRU cache size

    // For Platter Samples (In-memory LRU cache)
    std::map<std::string, std::shared_ptr<AudioSample>> platterSampleCache_;
    std::list<std::string> platterSampleLru_;

    // For Music Tracks (In-memory LRU cache)
    std::map<std::string, std::shared_ptr<AudioSample>> musicTrackCache_;
    std::list<std::string> musicTrackLru_;
    std::mutex cacheMutex_; // Mutex for protecting access to in-memory caches

    void manageCache(std::map<std::string, std::shared_ptr<AudioSample>>& cache,
                     std::list<std::string>& lruList,
                     const std::string& key,
                     std::shared_ptr<AudioSample> sampleToCache);
    void updateLru(std::list<std::string>& lruList, const std::string& key);

};

// Function to compute simple hash (replaces SHA256)
std::string computeSimpleHash(const std::string& input) {
    std::hash<std::string> hasher;
    size_t hashValue = hasher(input);
    
    std::stringstream ss;
    ss << std::hex << std::setw(16) << std::setfill('0') << hashValue;
    return ss.str();
}

std::string AudioEngine::getPersistentCacheFilePath(const std::string& originalPathOrUri, uint64_t modificationTimestamp) {
    if (baseCachePath_.empty()) {
        ALOGE("AudioEngine: baseCachePath_ is not set. Cannot generate persistent cache file path.");
        return ""; // Return empty if base path isn't set
    }

    std::string audioCacheSubDir = baseCachePath_ + "/audio_cache";
    // Ensure the directory exists
    struct stat st = {0};
    if (stat(audioCacheSubDir.c_str(), &st) == -1) {
        // Try to create. Note: mkdir might need parent directories to exist.
        // For simplicity, we try to create one level. Real apps might need recursive creation.
        if (mkdir(audioCacheSubDir.c_str(), 0700) == -1 && errno != EEXIST) {
             ALOGE("AudioEngine: Failed to create cache subdirectory %s. Error: %s", audioCacheSubDir.c_str(), strerror(errno));
             return ""; // Failed to create dir
        }
    }

    std::string cacheKeyString = originalPathOrUri + "_" + std::to_string(modificationTimestamp);
    std::string hashedName = computeSimpleHash(cacheKeyString);
    return audioCacheSubDir + "/" + hashedName + ".audiocache";
}


void AudioEngine::manageCache(std::map<std::string, std::shared_ptr<AudioSample>>& cache,
                            std::list<std::string>& lruList,
                            const std::string& key,
                            std::shared_ptr<AudioSample> sampleToCache) {
    lruList.remove(key); // Remove if exists to update its position
    lruList.push_front(key);
    cache[key] = sampleToCache;

    if (cache.size() > MAX_CACHED_SAMPLES_PER_TYPE) {
        if (!lruList.empty()) {
            std::string lruKey = lruList.back();
            lruList.pop_back();
            cache.erase(lruKey);
            ALOGI("AudioEngine::manageCache (In-Memory): Evicted %s from in-memory cache.", lruKey.c_str());
        }
    }
}

void AudioEngine::updateLru(std::list<std::string>& lruList, const std::string& key) {
    lruList.remove(key);
    lruList.push_front(key);
}


// CRITICAL FIX: DISABLE DUPLICATE hasExtension METHOD
/*
bool AudioSample::hasExtension(const std::string& path, const std::string& extension) {
    if (path.length() >= extension.length()) {
        std::string lowerFilePath = path;
        std::transform(lowerFilePath.begin(), lowerFilePath.end(), lowerFilePath.begin(),
                       [](unsigned char c){ return std::tolower(c); });
        return (0 == lowerFilePath.compare(lowerFilePath.length() - extension.length(), extension.length(), extension));
    }
    return false;
}
*/

// CRITICAL FIX: DISABLE DUPLICATE tryLoadPath METHOD
/*
bool AudioSample::tryLoadPath(AAssetManager* assetManager, const std::string& currentPathToTry) {
    audioData.clear(); totalFrames = 0; channels = 0; sampleRate = 0; bool success = false;
    AAsset* asset = AAssetManager_open(assetManager, currentPathToTry.c_str(), AASSET_MODE_BUFFER);
    if (!asset) { ALOGW("AudioSample: Failed to open asset: %s", currentPathToTry.c_str()); return false; }
    const void* assetBuffer = AAsset_getBuffer(asset);
    size_t assetLength = AAsset_getLength(asset);
    if (!assetBuffer) { ALOGW("AudioSample: Failed to get asset buffer for: %s", currentPathToTry.c_str()); AAsset_close(asset); return false; }

    if (hasExtension(currentPathToTry, ".wav")) {
        drwav wav;
        if (drwav_init_memory(&wav, assetBuffer, assetLength, nullptr)) {
            channels = wav.channels; totalFrames = (int32_t)wav.totalPCMFrameCount; sampleRate = wav.sampleRate;
            audioData.resize(static_cast<size_t>(totalFrames) * channels); // Use static_cast for size_t
            success = (drwav_read_pcm_frames_f32(&wav, totalFrames, audioData.data()) == static_cast<drwav_uint64>(totalFrames));
            drwav_uninit(&wav);
        }
    } else if (hasExtension(currentPathToTry, ".mp3")) {
        drmp3_config config; drmp3_uint64 pcmFrameCount;
        float* pPcmFrames = drmp3_open_memory_and_read_pcm_frames_f32(assetBuffer, assetLength, &config, &pcmFrameCount, nullptr);
        if (pPcmFrames) {
            channels = config.channels; sampleRate = config.sampleRate; totalFrames = (int32_t)pcmFrameCount;
            audioData.assign(pPcmFrames, pPcmFrames + (static_cast<size_t>(pcmFrameCount) * channels)); // Use static_cast for size_t
            drmp3_free(pPcmFrames, nullptr); success = true;
        }
    }
    AAsset_close(asset); return success;
}
*/

// CRITICAL FIX: DISABLE DUPLICATE load METHOD
/*
void AudioSample::load(AAssetManager* assetManager, const std::string& basePath, AudioEngine* engine, uint64_t modificationTimestamp) {
    if (!sincTableInitialized) { precalculateSincTable(); }
    this->audioEnginePtr = engine;
    this->filePath = basePath; // Store original path early for logging/cache key

    ALOGI("AudioSample::load (Asset): Attempting to load: %s, ModTime: %" PRIu64, basePath.c_str(), modificationTimestamp);
    isPlaying.store(false); preciseCurrentFrame.store(0.0f); useEngineRateForPlayback_.store(false);
    playedOnce = false; loop.store(false); playOnceThenLoopSilently = false;

    if (!assetManager) { ALOGE("AudioSample::load (Asset): AssetManager is null for %s!", basePath.c_str()); return; }
    if (!this->audioEnginePtr) { ALOGE("AudioSample::load (Asset): AudioEngine pointer is null for %s!", basePath.c_str()); return; }

    std::string persistentCacheFilePath = this->audioEnginePtr->getPersistentCacheFilePath(basePath, modificationTimestamp);
    if (!persistentCacheFilePath.empty()) {
        if (loadFromPersistentCache(persistentCacheFilePath, modificationTimestamp, basePath)) {
            ALOGI("AudioSample::load (Asset): Successfully loaded '%s' from persistent cache: %s", basePath.c_str(), persistentCacheFilePath.c_str());
            // Resampling to stream rate should have been handled before caching or is implicit in cache format.
            // However, let's double-check if the cached sampleRate matches the stream's desired rate.
            // This is important if the stream rate could change or if cache was from a different config.
            // For now, assume cache stores data at streamSampleRate. If not, resample here.
             if (this->audioEnginePtr->getStreamSampleRate() > 0 && this->sampleRate != this->audioEnginePtr->getStreamSampleRate()) {
                ALOGW("AudioSample::load (Asset): Cached sample rate %u for '%s' differs from stream rate %u. Resampling.", this->sampleRate, basePath.c_str(), this->audioEnginePtr->getStreamSampleRate());
                resampleDataTo(this->audioEnginePtr->getStreamSampleRate());
            }
            return; // Loaded from cache
        } else {
            ALOGI("AudioSample::load (Asset): Persistent cache miss or stale for '%s' (Path: %s). Loading from assets.", basePath.c_str(), persistentCacheFilePath.c_str());
        }
    } else {
         ALOGW("AudioSample::load (Asset): Could not generate persistent cache file path for %s. Proceeding without persistent cache.", basePath.c_str());
    }


    bool loadedSuccessfully = false; std::string successfulPathAttempt;
    // Try direct path first (if it includes extension)
    if (hasExtension(basePath, ".wav") || hasExtension(basePath, ".mp3")) {
        if (tryLoadPath(assetManager, basePath)) { loadedSuccessfully = true; successfulPathAttempt = basePath; }
    }
    // If not loaded, try appending extensions
    if (!loadedSuccessfully) {
        std::string pathWithMp3 = basePath + ".mp3";
        if (tryLoadPath(assetManager, pathWithMp3)) { loadedSuccessfully = true; successfulPathAttempt = pathWithMp3; }
    }
    if (!loadedSuccessfully) {
        std::string pathWithWav = basePath + ".wav";
        if (tryLoadPath(assetManager, pathWithWav)) { loadedSuccessfully = true; successfulPathAttempt = pathWithWav; }
    }

    if (loadedSuccessfully) {
        this->filePath = successfulPathAttempt; // Update filePath if an extension was appended
        // OPTIMIZED: Keep only essential decoding success logs
        ALOGI("AudioSample: Successfully decoded asset '%s' (%d frames)", this->filePath.c_str(), totalFrames);

        if (this->audioEnginePtr->getStreamSampleRate() > 0) {
            uint32_t targetSr = this->audioEnginePtr->getStreamSampleRate();
            if (this->sampleRate != targetSr && targetSr > 0 && this->sampleRate > 0) {
                // OPTIMIZED: Simplified resampling logging
                ALOGI("AudioSample: Resampling '%s' %u->%u Hz", this->filePath.c_str(), this->sampleRate, targetSr);
                resampleDataTo(targetSr);
                // ALOGI("AudioSample::load (Asset): Resampling complete for asset '%s'. New frames: %d, new SR: %u", this->filePath.c_str(), this->totalFrames, this->sampleRate);
            } else {
                // ALOGI("AudioSample::load (Asset): No resampling needed for asset '%s' (Original SR: %u Hz, Target SR: %u Hz)", this->filePath.c_str(), this->sampleRate, targetSr);
            }
            // Save to persistent cache after processing
            if (!persistentCacheFilePath.empty()) {
                saveToPersistentCache(persistentCacheFilePath, modificationTimestamp);
            }
        }
    } else {
        // filePath was already set to basePath at the start
        ALOGE("AudioSample::load (Asset): Failed to load ASSET for base '%s'", basePath.c_str());
        audioData.clear(); totalFrames = 0; channels = 0; sampleRate = 0;
    }
}
*/

// CRITICAL FIX: DISABLE DUPLICATE getAudio IMPLEMENTATION
// This duplicate implementation was causing 10x position jumps!
// AudioSample.cpp contains the correct implementation - use that one exclusively
/*
void AudioSample::getAudio(float* outputBuffer, int32_t numOutputFrames, int32_t outputStreamChannels,
                           float effectiveVolume) {
    // DEBUG: Identify which getAudio implementation is being called
    static int nativeLibCallCounter = 0;
    if (nativeLibCallCounter++ % 10 == 0) {
        ALOGI("GETAUDIO_CALL_TRACKING_NATIVE: Call #%d, BufferSize=%d", nativeLibCallCounter, numOutputFrames);
    }

    // DISABLED: This entire duplicate getAudio implementation was causing 10x position jumps
    /*
    // ===== HYBRID SYNC SYSTEM: FINGER vs MOTOR CONTROL =====
    // - FINGER CONTROL: This method advances position (traditional behavior)
    // - MOTOR CONTROL: Audio callback sets position via master tick system

    float localPreciseCurrentFrame = preciseCurrentFrame.load();
    float playbackRateToUse = 1.0f;
    bool isFingerControlled = false;

    if (audioEnginePtr != nullptr) {
        isFingerControlled = audioEnginePtr->isPlatterTouched();
        if (useEngineRateForPlayback_.load()) {
            playbackRateToUse = audioEnginePtr->platterTargetPlaybackRate_.load();
        }
    }

    if (!isPlaying.load() || audioData.empty() || totalFrames == 0 || channels == 0) {
        return;
    }

    // Process each sample in the buffer
    for (int i = 0; i < numOutputFrames; ++i) {
        if (!isPlaying.load()) {
            break;
        }

        // Calculate the frame position for this sample
        float currentFrame;
        if (isFingerControlled) {
            // FINGER CONTROL: Calculate position within this buffer (traditional method)
            currentFrame = localPreciseCurrentFrame + (float)i * playbackRateToUse;
        } else {
            // MOTOR CONTROL: Use the exact position set by audio callback tick system
            currentFrame = localPreciseCurrentFrame;
        }

        // Handle looping and bounds checking
        if (currentFrame >= static_cast<float>(totalFrames) || currentFrame < 0.0f) {
            if (playOnceThenLoopSilently && !playedOnce) {
                playedOnce = true;
                currentFrame = 0.0f;
                if (!loop.load()) loop.store(true);
            } else if (loop.load()) {
                if (totalFrames > 0) {
                    currentFrame = fmodf(currentFrame, static_cast<float>(totalFrames));
                    if (currentFrame < 0.0f) currentFrame += static_cast<float>(totalFrames);
                } else {
                    currentFrame = 0.0f;
                }
            } else {
                isPlaying.store(false);
                break;
            }
        }

        // High-quality sinc interpolation at the calculated position
        float fractionalTime = currentFrame - std::floor(currentFrame);
        int32_t baseFrameIndex = static_cast<int32_t>(std::floor(currentFrame));
        int sincTableIndex = static_cast<int>(fractionalTime * SUBDIVISION_STEPS);
        sincTableIndex = std::min(sincTableIndex, SUBDIVISION_STEPS - 1);
        const std::vector<float>& coefficients = sincTable[sincTableIndex];
        int32_t kernelStartFrameIndex = baseFrameIndex - (NUM_TAPS / 2 - 1);

        for (int ch_out = 0; ch_out < outputStreamChannels; ++ch_out) {
            int srcChannel = ch_out % channels;
            float interpolatedSample = 0.0f;
            for (int k = 0; k < NUM_TAPS; ++k) {
                float sampleValue = getSampleAt(kernelStartFrameIndex + k, srcChannel);
                interpolatedSample += sampleValue * coefficients[k];
            }

            // EXPONENTIAL FADER SMOOTHING: Per-sample analog behavior
            // currentVolume += (targetVolume - currentVolume) * smoothingFactor
            currentSmoothedVolume_ += (effectiveVolume - currentSmoothedVolume_) * FADER_SMOOTHING_FACTOR;

            outputBuffer[i * outputStreamChannels + ch_out] += interpolatedSample * currentSmoothedVolume_;
        }
    }

    // ===== CRITICAL FIX: DISABLE DUPLICATE POSITION ADVANCEMENT =====
    // AudioSample.cpp already handles position advancement correctly per sample
    // This duplicate per-buffer advancement was causing 10x position jumps!
    // DISABLED: Position advancement now handled exclusively by AudioSample.cpp
    /*
    if (isFingerControlled) {
        // FINGER CONTROL: Advance position normally (traditional behavior)
        float newPosition = localPreciseCurrentFrame + (float)numOutputFrames * playbackRateToUse;
        preciseCurrentFrame.store(newPosition);
    */

        // OPTIMIZED: Disabled frequent finger advancement logging for performance
        // Debug logging only occasionally (finger control confirmation in main callback is sufficient)
        /*
        static int fingerAdvanceLogCounter = 0;
        if (fingerAdvanceLogCounter++ % 2400 == 0) { // Every ~50ms at 48kHz
            ALOGI("FingerAdvance: %.1f -> %.1f (delta: %.1f, rate: %.3f)",
                  localPreciseCurrentFrame, newPosition, newPosition - localPreciseCurrentFrame, playbackRateToUse);
        }
        */
    }
    // MOTOR CONTROL: Position is managed by audio callback tick system - do NOT advance here!
    */
}

// CRITICAL FIX: DISABLE DUPLICATE loadFromMemory METHOD
/*
void AudioSample::loadFromMemory(const unsigned char* dataBuffer, size_t dataBufferSize, const std::string& identifier, uint64_t modificationTimestamp) {
    if (!sincTableInitialized) {
        precalculateSincTable();
    }
    this->filePath = identifier; // Store original identifier (URI string)
    isPlaying.store(false);
    preciseCurrentFrame.store(0.0f);
    useEngineRateForPlayback_.store(false);
    playedOnce = false;
    loop.store(false);
    playOnceThenLoopSilently = false;
    // audioData, totalFrames, channels, sampleRate will be cleared/set by cache load or decode

    ALOGI("AudioSample::loadFromMemory (User File): Attempting to load: %s, ModTime: %" PRIu64, identifier.c_str(), modificationTimestamp);

    if (!this->audioEnginePtr) { ALOGE("AudioSample::loadFromMemory (User File): AudioEngine pointer is null for %s!", identifier.c_str()); return; }

    std::string persistentCacheFilePath = this->audioEnginePtr->getPersistentCacheFilePath(identifier, modificationTimestamp);
    if (!persistentCacheFilePath.empty()) {
        if (loadFromPersistentCache(persistentCacheFilePath, modificationTimestamp, identifier)) {
            ALOGI("AudioSample::loadFromMemory (User File): Successfully loaded '%s' from persistent cache: %s", identifier.c_str(), persistentCacheFilePath.c_str());
            // Similar to asset loading, ensure cached sample rate is appropriate or resample.
            // Assume cache stores data at streamSampleRate.
             if (this->audioEnginePtr->getStreamSampleRate() > 0 && this->sampleRate != this->audioEnginePtr->getStreamSampleRate()) {
                ALOGW("AudioSample::loadFromMemory (User File): Cached sample rate %u for '%s' differs from stream rate %u. Resampling.", this->sampleRate, identifier.c_str(), this->audioEnginePtr->getStreamSampleRate());
                resampleDataTo(this->audioEnginePtr->getStreamSampleRate());
            }
            return; // Loaded from cache
        } else {
            ALOGI("AudioSample::loadFromMemory (User File): Persistent cache miss or stale for '%s' (Path: %s). Loading from memory buffer.", identifier.c_str(), persistentCacheFilePath.c_str());
        }
    } else {
        ALOGW("AudioSample::loadFromMemory (User File): Could not generate persistent cache file path for %s. Proceeding without persistent cache.", identifier.c_str());
    }

    // Clear any previous data if cache load failed partway or if proceeding to decode
    audioData.clear(); totalFrames = 0; channels = 0; sampleRate = 0;
    bool success = false;

    ALOGI("AudioSample::loadFromMemory (User File): Decoding from memory buffer for identifier: %s, buffer size: %zu bytes", identifier.c_str(), dataBufferSize);

    if (!dataBuffer || dataBufferSize == 0) {
        ALOGE("AudioSample::loadFromMemory (User File): Data buffer is null or empty for USER FILE %s.", identifier.c_str());
        return;
    }

    std::string lowerIdentifier = identifier;
    std::transform(lowerIdentifier.begin(), lowerIdentifier.end(), lowerIdentifier.begin(), ::tolower);

    // Decoding logic (WAV, MP3, auto-detect) - same as before
    if (lowerIdentifier.length() > 4 && lowerIdentifier.substr(lowerIdentifier.length() - 4) == ".wav") {
        drwav wav;
        if (drwav_init_memory(&wav, dataBuffer, dataBufferSize, nullptr)) {
            channels = wav.channels; totalFrames = (int32_t)wav.totalPCMFrameCount; sampleRate = wav.sampleRate;
            audioData.resize(static_cast<size_t>(totalFrames) * channels);
            success = (drwav_read_pcm_frames_f32(&wav, totalFrames, audioData.data()) == static_cast<drwav_uint64>(totalFrames));
            drwav_uninit(&wav);
            if (success) ALOGI("AudioSample::loadFromMemory (User File): Successfully decoded WAV from memory: %s", identifier.c_str());
            else ALOGE("AudioSample::loadFromMemory (User File): Failed to read PCM frames for WAV from memory: %s", identifier.c_str());
        } else ALOGE("AudioSample::loadFromMemory (User File): Failed to initialize WAV from memory: %s", identifier.c_str());
    } else if (lowerIdentifier.length() > 4 && lowerIdentifier.substr(lowerIdentifier.length() - 4) == ".mp3") {
        drmp3_config config; drmp3_uint64 pcmFrameCount;
        float* pPcmFrames = drmp3_open_memory_and_read_pcm_frames_f32(dataBuffer, dataBufferSize, &config, &pcmFrameCount, nullptr);
        if (pPcmFrames) {
            channels = config.channels; sampleRate = config.sampleRate; totalFrames = (int32_t)pcmFrameCount;
            audioData.assign(pPcmFrames, pPcmFrames + (static_cast<size_t>(pcmFrameCount) * channels));
            drmp3_free(pPcmFrames, nullptr); success = true;
            ALOGI("AudioSample::loadFromMemory (User File): Successfully decoded MP3 from memory: %s", identifier.c_str());
        } else ALOGE("AudioSample::loadFromMemory (User File): Failed to initialize MP3 from memory: %s", identifier.c_str());
    } else {
        ALOGW("AudioSample::loadFromMemory (User File): Unknown file type for memory loading: %s. Attempting WAV, then MP3.", identifier.c_str());
        drwav wav_try;
        if (drwav_init_memory(&wav_try, dataBuffer, dataBufferSize, nullptr)) {
            channels = wav_try.channels; totalFrames = (int32_t)wav_try.totalPCMFrameCount; sampleRate = wav_try.sampleRate;
            audioData.resize(static_cast<size_t>(totalFrames) * channels);
            success = (drwav_read_pcm_frames_f32(&wav_try, totalFrames, audioData.data()) == static_cast<drwav_uint64>(totalFrames));
            drwav_uninit(&wav_try);
            if(success) ALOGI("AudioSample::loadFromMemory (User File): Successfully decoded (tried WAV) from memory: %s", identifier.c_str());
        }
        if(!success) {
            ALOGI("AudioSample::loadFromMemory (User File): WAV attempt failed for %s, trying MP3.", identifier.c_str());
            drmp3_config config_try; drmp3_uint64 pcmFrameCount_try;
            float* pPcmFrames_try = drmp3_open_memory_and_read_pcm_frames_f32(dataBuffer, dataBufferSize, &config_try, &pcmFrameCount_try, nullptr);
            if (pPcmFrames_try) {
                channels = config_try.channels; sampleRate = config_try.sampleRate; totalFrames = (int32_t)pcmFrameCount_try;
                audioData.assign(pPcmFrames_try, pPcmFrames_try + (static_cast<size_t>(pcmFrameCount_try) * channels));
                drmp3_free(pPcmFrames_try, nullptr); success = true;
                ALOGI("AudioSample::loadFromMemory (User File): Successfully decoded (tried MP3) from memory: %s", identifier.c_str());
            }
        }
        if(!success) ALOGE("AudioSample::loadFromMemory (User File): Failed to decode from memory, tried WAV and MP3: %s", identifier.c_str());
    }

    if (success) {
        ALOGI("AudioSample::loadFromMemory (User File): Decoded USER FILE from memory '%s' (Frames: %d, Ch: %d, SR: %u Hz)",
              this->filePath.c_str(), totalFrames, channels, sampleRate);

        if (this->audioEnginePtr->getStreamSampleRate() > 0) {
            uint32_t targetSr = this->audioEnginePtr->getStreamSampleRate();
            if (this->sampleRate != targetSr && targetSr > 0 && this->sampleRate > 0) {
                ALOGI("AudioSample::loadFromMemory (User File): Resampling user file '%s' from %u Hz to %u Hz", this->filePath.c_str(), this->sampleRate, targetSr);
                resampleDataTo(targetSr);
                ALOGI("AudioSample::loadFromMemory (User File): Resampling complete for user file '%s'. New frames: %d, new SR: %u", this->filePath.c_str(), this->totalFrames, this->sampleRate);
            } else {
                ALOGI("AudioSample::loadFromMemory (User File): No resampling needed for user file '%s' (Original SR: %u Hz, Target SR: %u Hz)", this->filePath.c_str(), this->sampleRate, targetSr);
            }
            // Save to persistent cache after processing
            if (!persistentCacheFilePath.empty()) {
                saveToPersistentCache(persistentCacheFilePath, modificationTimestamp);
            }
        }
    } else {
        ALOGE("AudioSample::loadFromMemory (User File): Failed to decode USER FILE audio from memory for identifier '%s'", identifier.c_str());
        // Ensure data is cleared on full failure
        audioData.clear(); totalFrames = 0; channels = 0; sampleRate = 0;
    }
}
*/

// CRITICAL FIX: DISABLE DUPLICATE resampleDataTo METHOD
/*
void AudioSample::resampleDataTo(uint32_t targetSampleRate) {
    ALOGI("AudioSample::resampleDataTo: Starting OPTIMIZED resampling for '%s'. Original SR: %u, Target SR: %u, Original Frames: %d",
          this->filePath.c_str(), this->sampleRate, targetSampleRate, this->totalFrames);

    if (this->sampleRate == 0) {
        ALOGE("AudioSample::resampleDataTo: Original sample rate is 0. Cannot resample.");
        return;
    }
    if (targetSampleRate == this->sampleRate || this->totalFrames == 0 || this->channels == 0) {
        ALOGI("AudioSample::resampleDataTo: No resampling needed or not possible (target SR same, zero frames/channels). Original SR: %u, Target SR: %u, Frames: %d, Channels: %d",
              this->sampleRate, targetSampleRate, this->totalFrames, this->channels);
        return;
    }

    double ratio = static_cast<double>(targetSampleRate) / this->sampleRate;
    int32_t newTotalFrames = static_cast<int32_t>(std::round(static_cast<double>(this->totalFrames) * ratio));

    ALOGI("AudioSample::resampleDataTo: OPTIMIZED resampling ratio: %f, newTotalFrames: %d", ratio, newTotalFrames);

    if (newTotalFrames == 0) {
        ALOGW("AudioSample::resampleDataTo: newTotalFrames is 0 after ratio calculation. Clearing audio data.");
        this->audioData.clear();
        this->totalFrames = 0;
        return;
    }

    std::vector<float> resampledAudioData;
    resampledAudioData.reserve(static_cast<size_t>(newTotalFrames) * this->channels);

    // Optimized resampling: Special case for 44.1kHz -> 48kHz (most common)
    if (this->sampleRate == 44100 && targetSampleRate == 48000) {
        // Fast path: 44100->48000 = ratio of 160/147 (use integer math)
        ALOGI("AudioSample::resampleDataTo: Using fast 44.1->48kHz path");
        
        const size_t oldDataSize = this->audioData.size();
        for (int32_t j = 0; j < newTotalFrames; ++j) {
            // Integer math: j * 147 / 160 gives source frame position
            int32_t baseFrameIndex = (j * 147) / 160;
            int32_t remainder = (j * 147) % 160;
            float fractionalTime = static_cast<float>(remainder) / 160.0f;
            
            // Clamp to valid range
            int32_t nextFrameIndex = std::min(baseFrameIndex + 1, this->totalFrames - 1);
            baseFrameIndex = std::max(0, std::min(baseFrameIndex, this->totalFrames - 1));

            for (int ch = 0; ch < this->channels; ++ch) {
                size_t baseIdx = static_cast<size_t>(baseFrameIndex) * this->channels + ch;
                size_t nextIdx = static_cast<size_t>(nextFrameIndex) * this->channels + ch;
                
                float sample1 = (baseIdx < oldDataSize) ? this->audioData[baseIdx] : 0.0f;
                float sample2 = (nextIdx < oldDataSize) ? this->audioData[nextIdx] : 0.0f;
                
                // Linear interpolation
                float interpolatedSample = sample1 + (sample2 - sample1) * fractionalTime;
                resampledAudioData.push_back(interpolatedSample);
            }
        }
    } else {
        // General case (still optimized with pre-calculated step size)
        ALOGI("AudioSample::resampleDataTo: Using general optimized path");
        
        const double stepSize = 1.0 / ratio;
        const size_t oldDataSize = this->audioData.size();
        
        for (int32_t j = 0; j < newTotalFrames; ++j) {
            double sourceFrameEquivalent = static_cast<double>(j) * stepSize;
            int32_t baseFrameIndex = static_cast<int32_t>(sourceFrameEquivalent);
            float fractionalTime = static_cast<float>(sourceFrameEquivalent - baseFrameIndex);
            
            // Clamp to valid range
            int32_t nextFrameIndex = std::min(baseFrameIndex + 1, this->totalFrames - 1);
            baseFrameIndex = std::max(0, std::min(baseFrameIndex, this->totalFrames - 1));

            for (int ch = 0; ch < this->channels; ++ch) {
                size_t baseIdx = static_cast<size_t>(baseFrameIndex) * this->channels + ch;
                size_t nextIdx = static_cast<size_t>(nextFrameIndex) * this->channels + ch;
                
                float sample1 = (baseIdx < oldDataSize) ? this->audioData[baseIdx] : 0.0f;
                float sample2 = (nextIdx < oldDataSize) ? this->audioData[nextIdx] : 0.0f;
                
                // Linear interpolation
                float interpolatedSample = sample1 + (sample2 - sample1) * fractionalTime;
                resampledAudioData.push_back(interpolatedSample);
            }
        }
    }

    this->audioData = std::move(resampledAudioData);
    this->sampleRate = targetSampleRate;
    this->totalFrames = newTotalFrames;

    ALOGI("AudioSample::resampleDataTo: OPTIMIZED resampling successful for '%s'. New SR: %u, New Frames: %d",
          this->filePath.c_str(), this->sampleRate, this->totalFrames);
}
*/

bool AudioEngine::init(AAssetManager* mgr) {
    ALOGI("AudioEngine init. this: %p", this);
    appAssetManager_ = mgr; // Store the asset manager
    oboe::AudioStreamBuilder builder;
    builder.setDirection(oboe::Direction::Output)
            ->setPerformanceMode(oboe::PerformanceMode::LowLatency)
            ->setSharingMode(oboe::SharingMode::Exclusive)
            ->setFormat(oboe::AudioFormat::Float)
            ->setChannelCount(oboe::ChannelCount::Stereo)
            ->setCallback(this);
    ALOGI("AudioEngine init: Explicitly setting sample rate to 48000 Hz for Oboe stream.");
    builder.setSampleRate(48000);
    ALOGI("AudioEngine init: Attempting to open stream...");
    oboe::Result result = builder.openStream(audioStream_);
    ALOGI("AudioEngine init: openStream result: %s. audioStream_.get() after open: %p",
          oboe::convertToText(result), (audioStream_ ? audioStream_.get() : nullptr) );
    if (result == oboe::Result::OK && audioStream_ && audioStream_.get() != nullptr) {
        streamSampleRate_ = audioStream_->getSampleRate();
        ALOGI("Stream opened successfully: SR=%u, Channels=%d, Format=%s, Current State: %s",
              streamSampleRate_, audioStream_->getChannelCount(),
              oboe::convertToText(audioStream_->getFormat()),
              oboe::convertToText(audioStream_->getState()));
        // activePlatterSample_ and activeMusicSample_ are already null by default from constructor
        // No need to create AudioSample instances here; they are created on demand.
        ALOGI("AudioEngine init: Stream opened. Active samples are initially null.");
        return true;
    } else {
        ALOGE("Failed to open stream OR stream object is invalid. Oboe Result: %s. audioStream_.get(): %p",
              oboe::convertToText(result), (audioStream_ ? audioStream_.get() : nullptr) );
        if (audioStream_ && audioStream_.get() != nullptr) { audioStream_->close(); }
        audioStream_.reset();
        return false;
    }
}

void AudioEngine::release() {
    ALOGI("AudioEngine release.");
    
    // Clean up VinylTracker first
    if (vinylTracker_) {
        vinylTracker_.reset(); // This will properly stop tracking and clean up
        ALOGI("AudioEngine release: VinylTracker cleaned up.");
    }
    
    if (audioStream_) {
        stopStream(); // This internally checks if audioStream_ is valid
        audioStream_->close();
        audioStream_.reset();
    }

    { // Scope for lock guard
        std::lock_guard<std::mutex> lock(cacheMutex_);
        activePlatterSample_.reset();
        activeMusicSample_.reset();
        platterSampleCache_.clear();
        platterSampleLru_.clear();
        musicTrackCache_.clear();
        musicTrackLru_.clear();
        ALOGI("AudioEngine release: Active samples reset and in-memory caches cleared.");
    }
    // Note: Persistent cache on disk is not cleared here. That would require separate logic.

    appAssetManager_ = nullptr; // Clear asset manager
}

oboe::Result AudioEngine::startStream() {
    ALOGI("AudioEngine::startStream() ENTRY. audioStream_.get(): %p", (audioStream_ ? audioStream_.get() : nullptr) );
    if (!audioStream_ || !audioStream_.get()) { ALOGE("Stream not initialized for startStream!"); return oboe::Result::ErrorNull; }
    oboe::StreamState currentState = audioStream_->getState();
    ALOGI("AudioEngine: Current stream state before requestStart: %s", oboe::convertToText(currentState));
    if (currentState == oboe::StreamState::Started || currentState == oboe::StreamState::Starting) { ALOGW("Stream already started/starting."); return oboe::Result::OK; }
    if (currentState == oboe::StreamState::Closed || currentState == oboe::StreamState::Disconnected) { ALOGE("Stream closed/disconnected (%s). Cannot start.", oboe::convertToText(currentState)); return oboe::Result::ErrorClosed; }
    oboe::Result result = audioStream_->requestStart();
    ALOGI("AudioEngine: audioStream_->requestStart() result: %s. State after: %s", oboe::convertToText(result), oboe::convertToText(audioStream_->getState()));
    return result;
}

oboe::Result AudioEngine::stopStream() {
    ALOGI("AudioEngine::stopStream() ENTRY. audioStream_.get(): %p", (audioStream_ ? audioStream_.get() : nullptr) );
    if (!audioStream_ || !audioStream_.get()) { ALOGE("Stream not initialized for stopStream!"); return oboe::Result::ErrorNull; }
    oboe::StreamState currentState = audioStream_->getState();
    ALOGI("AudioEngine: Current stream state before requestStop: %s", oboe::convertToText(currentState));
    if (currentState == oboe::StreamState::Stopped || currentState == oboe::StreamState::Stopping) { ALOGW("Stream already stopped/stopping."); return oboe::Result::OK; }
    if (currentState == oboe::StreamState::Closed || currentState == oboe::StreamState::Disconnected) { ALOGE("Stream closed/disconnected (%s). Cannot stop.", oboe::convertToText(currentState)); return oboe::Result::ErrorClosed; }
    oboe::Result result = audioStream_->requestStop();
    ALOGI("AudioEngine: audioStream_->requestStop() result: %s. State after: %s", oboe::convertToText(result), oboe::convertToText(audioStream_->getState()));
    return result;
}

void AudioEngine::stopMusicTrackInternal() {
    ALOGI("AudioEngine: stopMusicTrackInternal");
    if (activeMusicSample_) {
        activeMusicSample_->isPlaying.store(false);
        ALOGI("Stopped music track: %s", activeMusicSample_->filePath.c_str());
    } else {
        ALOGW("stopMusicTrackInternal: activeMusicSample_ is null.");
    }
}

void AudioEngine::setPlatterFaderVolumeInternal(float volume) {
    float clampedVolume = std::clamp(volume, 0.0f, 1.0f);
    platterFaderVolume_.store(clampedVolume);
    ALOGV("AudioEngine: Platter Fader Volume set to %f", clampedVolume); // Changed to ALOGV for less noise
}

void AudioEngine::setMusicMasterVolumeInternal(float volume) {
    float clampedVolume = std::clamp(volume, 0.0f, 1.0f);
    generalMusicVolume_.store(clampedVolume);
    ALOGI("AudioEngine: Music Master Volume set to %f", clampedVolume);
}

void AudioEngine::scratchPlatterActiveInternal(bool isActiveTouch, float angleDeltaOrRateFromViewModel) {
    // ===== BASIC FLOW DEBUG: Always log to track execution =====
    ALOGE("BASIC_FLOW: isActiveTouch=%d, angleDelta=%.4f", isActiveTouch, angleDeltaOrRateFromViewModel);
    
    // ===== COORDINATE FIX VERIFICATION =====
    // Enhanced debugging to verify if UI coordinate compensation fixed the position corruption
    static int debugCounter = 0;
    if (debugCounter++ % 10 == 0) { // Log every 10th call to avoid spam
        ALOGI("COORDINATE_FIX: Touch=%d, AngleDelta=%.4f (should be smoother if fix worked)", 
              isActiveTouch, angleDeltaOrRateFromViewModel);
    }
    
    // ===== CONTROL MODE TRACKING: Log all mode transitions =====
    static bool wasLastTouch = false;
    static float lastRate = 0.0f;
    
    if (wasLastTouch != isActiveTouch) {
        ALOGI("CONTROL_MODE_TRANSITION: %s -> %s (Rate: %.4f -> %.4f)", 
              wasLastTouch ? "TOUCH" : "MOTOR", 
              isActiveTouch ? "TOUCH" : "MOTOR",
              lastRate, angleDeltaOrRateFromViewModel);
        wasLastTouch = isActiveTouch;
    }
    lastRate = angleDeltaOrRateFromViewModel;
    
    ALOGV_SCRATCH("AudioEngine::scratchPlatterActiveInternal - Input: isActiveTouch:%d, angleDeltaOrRate:%.4f", isActiveTouch, angleDeltaOrRateFromViewModel);
    isFingerDownOnPlatter_.store(isActiveTouch);

    if (!activePlatterSample_ || activePlatterSample_->totalFrames == 0) {
        if(isActiveTouch) ALOGW("ScratchPlatterActive: Attempt on unloaded/invalid active platter sample.");
        if(activePlatterSample_) { activePlatterSample_->useEngineRateForPlayback_.store(false); }
        ALOGV_SCRATCH("AudioEngine::scratchPlatterActiveInternal - ActivePlatterSample State: useEngineRate:%d, isPlaying:%d",
              (activePlatterSample_ ? activePlatterSample_->useEngineRateForPlayback_.load() : -1),
              (activePlatterSample_ ? activePlatterSample_->isPlaying.load() : -1 ));
        return;
    }

    activePlatterSample_->useEngineRateForPlayback_.store(true);
    ALOGV_SCRATCH("AudioEngine::scratchPlatterActiveInternal - ActivePlatterSample State (after useEngineRate=true): useEngineRate:%d, isPlaying:%d", activePlatterSample_->useEngineRateForPlayback_.load(), activePlatterSample_->isPlaying.load());

    float targetAudioRate;
    ALOGV_SCRATCH("ScratchPlatterActive INPUT (Detail): isActiveTouch: %d, angleDeltaOrRateFromVM: %.4f, degreesPerFrameUnity: %.4f",
          isActiveTouch, angleDeltaOrRateFromViewModel, degreesPerFrameForUnityRate_);

    if (isActiveTouch) {
        ALOGE("TOUCH_PATH_DEBUG: isActiveTouch=true, angleDelta=%.4f, threshold=%.4f", 
              angleDeltaOrRateFromViewModel, MOVEMENT_THRESHOLD);
        
        if (std::fabs(angleDeltaOrRateFromViewModel) > MOVEMENT_THRESHOLD) {
            ALOGE("MOVEMENT_DETECTED: Above threshold, processing normalization");
            float normalizedInputRate = 0.0f;
            if (std::fabs(degreesPerFrameForUnityRate_) > 1e-5f) {
                normalizedInputRate = angleDeltaOrRateFromViewModel / degreesPerFrameForUnityRate_;
                
                // ===== CRITICAL DEBUG: Track the exact normalization calculation =====
                ALOGE("NORMALIZATION_DEBUG: Input=%.4f, UnityRate=%.4f, Normalized=%.4f (Input/Unity)", 
                      angleDeltaOrRateFromViewModel, degreesPerFrameForUnityRate_, normalizedInputRate);
            } else if (std::fabs(angleDeltaOrRateFromViewModel) > 1e-5f) {
                normalizedInputRate = angleDeltaOrRateFromViewModel; // Fallback if degreesPerFrameForUnityRate_ is zero
                ALOGE("NORMALIZATION_DEBUG: FALLBACK - Unity rate too small, using raw input=%.4f", 
                      normalizedInputRate);
            }
            targetAudioRate = normalizedInputRate;
            targetAudioRate = std::clamp(targetAudioRate, -4.0f, 4.0f);
            if (!activePlatterSample_->isPlaying.load()) { activePlatterSample_->isPlaying.store(true); }
        } else {
            targetAudioRate = 0.0f;
            if (activePlatterSample_->isPlaying.load()) { activePlatterSample_->isPlaying.store(false); }
        }
    } else {
        targetAudioRate = angleDeltaOrRateFromViewModel;
        if (std::fabs(targetAudioRate) > 1e-5f) {
            if(!activePlatterSample_->isPlaying.load()) { activePlatterSample_->isPlaying.store(true); }
        } else {
            if(activePlatterSample_->isPlaying.load()) { activePlatterSample_->isPlaying.store(false); }
        }
    }
    
    ALOGV_SCRATCH("AudioEngine::scratchPlatterActiveInternal - Calculated: targetAudioRate:%.4f", targetAudioRate);
    
    // ===== ENHANCED RATE FLOW DEBUGGING =====
    static bool wasManualLastTime = false;
    bool isManualNow = isActiveTouch;
    
    // Log detailed rate conversion flow
    float normalizedRate = (isActiveTouch && std::fabs(degreesPerFrameForUnityRate_) > 1e-5f) ? 
                          angleDeltaOrRateFromViewModel / degreesPerFrameForUnityRate_ : angleDeltaOrRateFromViewModel;
    
    ALOGE("RATE_FLOW_MANUAL: wasManual=%d, physicsRate=%.4f, storedRate=%.4f, newRate=%.4f, angleDelta=%.4f", 
          wasManualLastTime, normalizedRate, platterTargetPlaybackRate_.load(), targetAudioRate, angleDeltaOrRateFromViewModel);
    
    // ===== DIRECTION DEBUG: Track reverse vs forward rate handling =====
    if (angleDeltaOrRateFromViewModel < 0) {
        ALOGE("DIRECTION_DEBUG: REVERSE detected - Input=%.4f, Normalized=%.4f, Target=%.4f", 
              angleDeltaOrRateFromViewModel, normalizedRate, targetAudioRate);
    } else if (angleDeltaOrRateFromViewModel > 0) {
        ALOGE("DIRECTION_DEBUG: FORWARD detected - Input=%.4f, Normalized=%.4f, Target=%.4f", 
              angleDeltaOrRateFromViewModel, normalizedRate, targetAudioRate);
    }
    
    wasManualLastTime = isManualNow;
    
    // ===== POSITION CORRUPTION TRACKING: Monitor position before rate changes =====
    float positionBeforeRateChange = activePlatterSample_ ? activePlatterSample_->preciseCurrentFrame.load() : 0.0f;
    
    // Apply physical speed limiter - CRITICAL: Pass touch state for proper handling
    float limitedRate = limitPlatterSpeed(targetAudioRate, isActiveTouch);
    platterTargetPlaybackRate_.store(limitedRate);
    
    // ===== POSITION CORRUPTION DETECTION: Check for immediate position jumps =====
    float positionAfterRateChange = activePlatterSample_ ? activePlatterSample_->preciseCurrentFrame.load() : 0.0f;
    if (std::abs(positionAfterRateChange - positionBeforeRateChange) > 10.0f) {
        ALOGE("POSITION_CORRUPTION_DETECTED: Rate change caused position jump %.2f -> %.2f (delta=%.2f)",
              positionBeforeRateChange, positionAfterRateChange, 
              positionAfterRateChange - positionBeforeRateChange);
    }
    
    ALOGV_SCRATCH("AudioEngine::scratchPlatterActiveInternal - Speed limited: %.4f -> %.4f", targetAudioRate, limitedRate);
    ALOGV_SCRATCH("AudioEngine::scratchPlatterActiveInternal - Final ActivePlatterSample State: useEngineRate:%d, isPlaying:%d", activePlatterSample_->useEngineRateForPlayback_.load(), activePlatterSample_->isPlaying.load());
}

void AudioEngine::releasePlatterTouchInternal() {
    ALOGI("AudioEngine: releasePlatterTouchInternal - CRITICAL SYNC POINT");
    isFingerDownOnPlatter_.store(false);
    
    if (activePlatterSample_) {
        // ===== CRITICAL FIX: SYNC MASTER TICK TO CURRENT AUDIO POSITION =====
        // This prevents audio jumps when transitioning from finger to motor control
        float currentAudioFrame = activePlatterSample_->preciseCurrentFrame.load();
        uint64_t ticksPerRotation = masterTickSystem_.getTicksPerVinylRotation();
        
        if (ticksPerRotation > 0 && activePlatterSample_->totalFrames > 0) {
            // Calculate the exact tick that corresponds to the current audio position
            float audioProgress = currentAudioFrame / (float)activePlatterSample_->totalFrames;
            // Handle looping - get the position within current rotation
            audioProgress = fmodf(audioProgress, 1.0f);
            if (audioProgress < 0.0f) audioProgress += 1.0f;
            
            uint64_t targetTickInRotation = (uint64_t)(audioProgress * (float)ticksPerRotation);
            
            // RESET the master tick to match the audio position exactly
            // This ensures seamless transition with no audio jumps
            masterTickSystem_.reset();
            masterTickSystem_.incrementTick(targetTickInRotation);
            
            ALOGI("SEAMLESS_SYNC: AudioFrame=%.1f/%.1f (%.3f%%), ResetTick=%lu, TicksPerRot=%lu",
                  currentAudioFrame, (float)activePlatterSample_->totalFrames, audioProgress * 100.0f,
                  targetTickInRotation, ticksPerRotation);
        }
        
        activePlatterSample_->useEngineRateForPlayback_.store(true);
        ALOGI("AudioEngine: Seamless transition to motor control. Rate: %.4f", platterTargetPlaybackRate_.load());
    }
}

oboe::DataCallbackResult AudioEngine::onAudioReady(oboe::AudioStream* stream, void* audioData, int32_t numFrames) {
    // ===== PERFECT VINYL SYNC: SINGLE SOURCE OF TRUTH =====
    // The master tick system is our ONLY timing reference for perfect vinyl emulation
    masterTickSystem_.incrementTick(numFrames);
    
    // CRITICAL: Update sample rate if stream rate changed
    uint32_t currentStreamRate = stream->getSampleRate();
    if (masterTickSystem_.getSampleRate() != currentStreamRate) {
        masterTickSystem_.setSampleRate(currentStreamRate);
        ALOGI("MasterTick: Stream rate updated to %u Hz", currentStreamRate);
    }
    
    auto* outputBuffer = static_cast<float*>(audioData);
    const int32_t channelCount = stream->getChannelCount();
    memset(outputBuffer, 0, static_cast<size_t>(numFrames) * channelCount * sizeof(float));

    // Apply volume smoothing to prevent clicks when sliders move
    float targetPlatterVol = platterFaderVolume_.load();
    float targetMusicVol = generalMusicVolume_.load();
    
    // EXPONENTIAL FADER SMOOTHING: Per-sample analog-like behavior
    // Instant response to big moves, smooth micro-adjustments
    // Note: Smoothing is applied per-sample in the audio processing loop for true analog feel

    // ===== PERFECT VINYL-AUDIO MAPPING =====
    if (activePlatterSample_) {
        uint64_t currentTick = masterTickSystem_.getCurrentTick();
        uint64_t ticksPerRotation = masterTickSystem_.getTicksPerVinylRotation();
        
        // CRITICAL FIX: Only use tick-based positioning when NOT under finger control
        // When finger is controlling, let the scratch system manage the position
        bool isFingerDown = isFingerDownOnPlatter_.load();
        bool useEngineRate = activePlatterSample_->useEngineRateForPlayback_.load();

        // CRITICAL FIX: COMPLETELY DISABLE MOTOR CONTROL POSITION OVERRIDE
        // This was causing directional overshoots - motor control was applying additional
        // movement in the same direction as manual control, causing 10x jumps!
        /*
        if (ticksPerRotation > 0 && activePlatterSample_->totalFrames > 0 &&
            !isFingerDown && useEngineRate) {

            // MOTOR CONTROL MODE: Use tick-based positioning for perfect motor sync
            uint64_t ticksInCurrentRotation = currentTick % ticksPerRotation;
            float exactAudioFrame = ((float)ticksInCurrentRotation / (float)ticksPerRotation) * (float)activePlatterSample_->totalFrames;

            // DEBUG: Log when motor control sets position to catch unwanted position jumps
            float currentAudioFrame = activePlatterSample_->preciseCurrentFrame.load();
            float positionJump = exactAudioFrame - currentAudioFrame;

            static int motorPositionLogCounter = 0;
            if (motorPositionLogCounter++ % 10 == 0 || abs(positionJump) > 50.0f) { // FREQUENT LOGGING to catch conflicts
                ALOGI("MOTOR_POSITION_SET: Current=%.1f -> New=%.1f (Jump=%.1f), FingerDown=%d, UseEngine=%d",
                      currentAudioFrame, exactAudioFrame, positionJump, isFingerDown, useEngineRate);
            }

            // Apply playback rate scaling for motor control
            float playbackRate = platterTargetPlaybackRate_.load();
            if (std::abs(playbackRate - 1.0f) > 0.001f) {
                exactAudioFrame *= playbackRate;

                // Handle looping with rate scaling
                if (exactAudioFrame >= (float)activePlatterSample_->totalFrames) {
                    exactAudioFrame = fmodf(exactAudioFrame, (float)activePlatterSample_->totalFrames);
                } else if (exactAudioFrame < 0.0f) {
                    exactAudioFrame = (float)activePlatterSample_->totalFrames + fmodf(exactAudioFrame, (float)activePlatterSample_->totalFrames);
                }
            }

            // CRITICAL FIX: DISABLE motor position override - this causes 10x position jumps!
            // The AudioSample.cpp position tracking is correct, motor override corrupts it
            // DISABLED: activePlatterSample_->preciseCurrentFrame.store(exactAudioFrame);

            // Debug logging for motor control (throttled)
            // OPTIMIZED: Reduced motor control logging frequency
            static uint64_t lastMotorLogTick = 0;
            if (currentTick - lastMotorLogTick > 240000) { // Every ~5 seconds at 48kHz (reduced from 1s)
                float vinylAngle = masterTickSystem_.ticksToVinylAngle(currentTick);
                ALOGI("MotorSync: Tick=%lu, Angle=%.1f°, Rate=%.3f",
                      currentTick, vinylAngle, playbackRate);
                lastMotorLogTick = currentTick;
            }
        }
        */
        // FINGER CONTROL MODE: Position is managed by scratch system, don't override!
        else if (isFingerDownOnPlatter_.load()) {
            // Let the existing scratch system control the position
            // The finger input drives the audio position directly
            // OPTIMIZED: Reduced finger control logging frequency
            static int fingerLogCounter = 0;
            if (fingerLogCounter++ % 24000 == 0) { // Every ~500ms at 48kHz (reduced from 50ms)
                float currentPos = activePlatterSample_->preciseCurrentFrame.load();
                ALOGI("FingerControl: AudioFrame=%.1f", currentPos);
            }
        }
        
        float platterVol = targetPlatterVol; // Direct target volume - smoothing now per-sample
        // Special handling for intro sound volume before first interaction
        if (activePlatterSample_->playOnceThenLoopSilently &&
            !activePlatterSample_->playedOnce &&
            !isFingerDownOnPlatter_.load() &&
            !activePlatterSample_->useEngineRateForPlayback_.load()
                ) {
            platterVol = targetMusicVol; // Use target music volume directly
        }
        activePlatterSample_->getAudio(outputBuffer, numFrames, channelCount, platterVol);
    }

    if (activeMusicSample_ && activeMusicSample_->isPlaying.load()) {
        activeMusicSample_->getAudio(outputBuffer, numFrames, channelCount, targetMusicVol); // Direct target volume
    }
    return oboe::DataCallbackResult::Continue;
}

void AudioEngine::onErrorBeforeClose(oboe::AudioStream *stream, oboe::Result error) { ALOGE("Oboe error before close: %s", oboe::convertToText(error)); }
void AudioEngine::onErrorAfterClose(oboe::AudioStream *stream, oboe::Result error) { ALOGE("Oboe error after close: %s", oboe::convertToText(error)); }

std::unique_ptr<AudioEngine> gAudioEngine = nullptr;


// Standalone VinylTracker class - 360Hz audio-synchronized angle tracking
class VinylTracker {
private:
    std::atomic<bool> isTracking_{false};
    std::atomic<float> currentAngle_{0.0f};
    std::atomic<float> unwrappedAngle_{0.0f}; // Never resets, tracks total rotation
    pthread_t trackingThread_;
    std::atomic<bool> shouldStop_{false};
    bool threadCreated_{false};
    AudioEngine* audioEngine_;
    
    static void* trackingLoopStatic(void* arg) {
        VinylTracker* tracker = static_cast<VinylTracker*>(arg);
        tracker->trackingLoop();
        return nullptr;
    }
    
    void trackingLoop() {
        ALOGI("VinylTracker: Tracking loop started with SEAMLESS MASTER TICK SYNC");
        const int64_t frameTimeUs = 2778; // 360Hz = 2.778ms = 2778 microseconds

        uint64_t lastMasterTick = 0;
        uint64_t baseTickOffset = 0;
        bool firstIteration = true;
        
        while (!shouldStop_.load()) {
            if (isTracking_.load() && audioEngine_) {
                // ===== SEAMLESS SYNC: DETECT TICK RESETS =====
                auto* masterTickSystem = audioEngine_->getMasterTickSystem();
                if (!masterTickSystem) {
                    usleep(frameTimeUs);
                    continue;
                }

                uint64_t currentMasterTick = masterTickSystem->getCurrentTick();

                // DETECT SEAMLESS SYNC: Check if tick went backwards (indicates reset/sync)
                if (currentMasterTick < lastMasterTick && !firstIteration) {
                    ALOGI("VinylTracker: SEAMLESS SYNC detected - Tick reset from %lu to %lu", 
                          lastMasterTick, currentMasterTick);
                    // Reset our tracking for seamless transition
                    baseTickOffset = 0;
                    firstIteration = true;
                }

                // Initialize or reinitialize on first iteration or after sync
                if (firstIteration) {
                    baseTickOffset = currentMasterTick;
                    lastMasterTick = currentMasterTick;
                    firstIteration = false;
                    ALOGI("VinylTracker: SEAMLESS tracking initialized - BaseOffset: %lu", baseTickOffset);
                }

                // Calculate relative tick position from tracking start/sync point
                uint64_t relativeTick = currentMasterTick - baseTickOffset;

                // Use master tick system to calculate angles directly
                float wrappedAngle = masterTickSystem->ticksToVinylAngle(relativeTick);
                float unwrappedAngle = masterTickSystem->ticksToUnwrappedAngle(relativeTick);

                // Store the calculated angles
                currentAngle_.store(wrappedAngle);
                unwrappedAngle_.store(unwrappedAngle);

                // OPTIMIZED: Reduced VinylTracker logging frequency  
                static int logCounter = 0;
                if (logCounter++ % 18000 == 0) { // Every ~50 seconds at 360Hz (reduced from 5s)
                    uint64_t ticksPerRotation = masterTickSystem->getTicksPerVinylRotation();
                    ALOGI("VinylTracker: Tick=%lu, Angle=%.1f°", currentMasterTick, wrappedAngle);
                }

                lastMasterTick = currentMasterTick;
            } else {
                // Reset on tracking stop/start
                firstIteration = true;
            }
            
            // Sleep for 2.778ms (360Hz)
            usleep(frameTimeUs);
        }
        ALOGI("VinylTracker: Tracking loop stopped");
    }
    
public:
    VinylTracker(AudioEngine* engine) : threadCreated_(false), audioEngine_(engine) {
        ALOGI("VinylTracker: Constructor with audio sync");
    }
    
    ~VinylTracker() {
        ALOGI("VinylTracker: Destructor");
        stopTracking();
    }
    
    void startTracking() {
        if (isTracking_.load()) {
            ALOGW("VinylTracker: Already tracking");
            return;
        }
        
        shouldStop_.store(false);
        isTracking_.store(true);
        currentAngle_.store(0.0f);
        
        int result = pthread_create(&trackingThread_, nullptr, trackingLoopStatic, this);
        if (result == 0) {
            threadCreated_ = true;
            ALOGI("VinylTracker: Started tracking");
        } else {
            ALOGE("VinylTracker: Failed to create thread: %d", result);
            isTracking_.store(false);
        }
    }
    
    void stopTracking() {
        if (!isTracking_.load()) {
            return;
        }
        
        isTracking_.store(false);
        shouldStop_.store(true);
        
        if (threadCreated_) {
            pthread_join(trackingThread_, nullptr);
            threadCreated_ = false;
        }
        
        ALOGI("VinylTracker: Stopped tracking");
    }
    
    float getCurrentAngle() const {
        return currentAngle_.load();
    }
    
    float getUnwrappedAngle() const {
        return unwrappedAngle_.load();
    }
    
    void resetAngle() {
        ALOGI("VinylTracker: Angle reset");
        unwrappedAngle_.store(0.0f);
        currentAngle_.store(0.0f);
    }
    
    bool isTracking() const {
        return isTracking_.load();
    }
};

// AudioEngine VinylTracker wrapper method implementations
void AudioEngine::startVinylTracking() {
    if (vinylTracker_) {
        vinylTracker_->startTracking();
    }
}

void AudioEngine::stopVinylTracking() {
    if (vinylTracker_) {
        vinylTracker_->stopTracking();
    }
}

float AudioEngine::getCurrentVinylAngle() {
    if (vinylTracker_) {
        return vinylTracker_->getCurrentAngle();
    }
    return 0.0f;
}

bool AudioEngine::isVinylTracking() {
    if (vinylTracker_) {
        return vinylTracker_->isTracking();
    }
    return false;
}

// Implementation of AudioEngine::testVinylTracker() after VinylTracker class definition
void AudioEngine::testVinylTracker() {
    if (vinylTracker_) {
        vinylTracker_->startTracking();
        ALOGI("AudioEngine: VinylTracker test started");
    } else {
        ALOGE("AudioEngine: VinylTracker is null, cannot start test");
    }
}

// ===== PERFECT VINYL SYNC: MASTER TICK SYSTEM IMPLEMENTATIONS =====

float AudioEngine::getCurrentTickBasedVinylAngle() const {
    uint64_t currentTick = masterTickSystem_.getCurrentTick();
    float tickBasedAngle = masterTickSystem_.ticksToVinylAngle(currentTick);

    // Periodic logging for validation (every ~5 seconds)
    static uint64_t lastLogTick = 0;
    uint64_t ticksSinceLastLog = currentTick - lastLogTick;
    uint32_t sampleRate = masterTickSystem_.getSampleRate();
    if (sampleRate > 0 && ticksSinceLastLog > (sampleRate * 5)) { // 5 seconds
        uint64_t ticksPerRotation = masterTickSystem_.getTicksPerVinylRotation();
        ALOGI("TickSystem: Current tick: %lu, Angle: %.2f°, Ticks/rotation: %lu, Sample rate: %u",
              currentTick, tickBasedAngle, ticksPerRotation, sampleRate);
        lastLogTick = currentTick;
    }

    return tickBasedAngle;
}

void AudioEngine::resetMasterTicks() {
    masterTickSystem_.reset();
    ALOGI("Master tick system reset");
}

// Helper method to setup tick system when platter sample changes
void AudioEngine::setupTicksPerVinylRotation() {
    if (activePlatterSample_ && activePlatterSample_->totalFrames > 0) {
        // One full sample = one full vinyl rotation
        uint64_t ticksPerRotation = static_cast<uint64_t>(activePlatterSample_->totalFrames);
        masterTickSystem_.setTicksPerVinylRotation(ticksPerRotation);
        ALOGI("MasterTickSystem: Set ticks per vinyl rotation to %lu (sample frames: %d)",
              ticksPerRotation, activePlatterSample_->totalFrames);
        
        // Reset tick counter for perfect sync on sample change
        masterTickSystem_.reset();
        ALOGI("MasterTickSystem: Reset tick counter for perfect sync with new sample");
    } else {
        masterTickSystem_.setTicksPerVinylRotation(0);
        ALOGW("MasterTickSystem: No valid platter sample, ticks per rotation set to 0");
    }
}
