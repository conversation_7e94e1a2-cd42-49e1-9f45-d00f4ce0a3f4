#include "AudioEngine.h"
#include "AudioSample.h"
#include <android/log.h>
#include <sys/stat.h>
#include <unistd.h>
#include <cerrno>
#include <cstring>
#include <sstream>
#include <iomanip>
#include <functional>
#include <cmath>
#include <algorithm>
#include <pthread.h>
#include <thread>
#include <chrono>
#include <cinttypes>

#define APP_TAG "AudioEngine"
#define ALOGI(...) __android_log_print(ANDROID_LOG_INFO, APP_TAG, __VA_ARGS__)
#define ALOGE(...) __android_log_print(ANDROID_LOG_ERROR, APP_TAG, __VA_ARGS__)
#define ALOGW(...) __android_log_print(ANDROID_LOG_WARN, APP_TAG, __VA_ARGS__)
#define ALOGV(...) __android_log_print(ANDROID_LOG_VERBOSE, APP_TAG, __VA_ARGS__)
#define ALOGV_SCRATCH(...) ((void)0)  // Disable verbose scratch logging


static std::string computeSimpleHash(const std::string& input) {
    std::hash<std::string> hasher;
    size_t hashValue = hasher(input);
    std::stringstream ss;
    ss << std::hex << std::setw(16) << std::setfill('0') << hashValue;
    return ss.str();
}

AudioEngine::AudioEngine() : streamSampleRate_(0),
                    activePlatterSample_(nullptr),
                    activeMusicSample_(nullptr) {
    ALOGI("AudioEngine default constructor.");
    currentPlatterSampleIndex_.store(0);
    currentMusicTrackIndex_.store(0);
    platterFaderVolume_.store(0.0f);
    generalMusicVolume_.store(0.9f);
    isFingerDownOnPlatter_.store(false);
    platterTargetPlaybackRate_.store(1.0f);

    
    // Initialize last volumes to match atomic values
    lastPlatterVolume_ = 0.0f;
    lastMusicVolume_ = 0.9f;
    internalAssetPlatterSamplePaths_ = {"sounds/haahhh", "sounds/sample1", "sounds/sample2"};
    internalAssetMusicTrackPaths_    = {"tracks/trackA", "tracks/trackB"};
    
    // Initialize VinylTracker with audio sync
    vinylTracker_ = std::make_unique<VinylTracker>(this);
    ALOGI("AudioEngine Constructor: VinylTracker initialized with audio sync");
    

}
AudioEngine::~AudioEngine() {
    ALOGI("AudioEngine destructor.");

    // Clean up JNI global reference
    if (javaVM_ && mainActivityGlobalRef_) {
        JNIEnv* env;
        if (javaVM_->GetEnv((void**)&env, JNI_VERSION_1_6) == JNI_OK) {
            env->DeleteGlobalRef(mainActivityGlobalRef_);
            mainActivityGlobalRef_ = nullptr;
        }
    }

    release();
}

float AudioEngine::limitPlatterSpeed(float requestedSpeed) {
    float curMax = maxPlatterSpeed_.load();
    if (curMax < 0.1f) curMax = 0.1f; // safety lower bound
    if (curMax > 5.0f) curMax = 5.0f; // safety upper bound
    float clampedSpeed = std::clamp(requestedSpeed, -curMax, curMax);
    previousLimitedSpeed = previousLimitedSpeed * SPEED_LIMIT_SMOOTHING +
                           clampedSpeed * (1.0f - SPEED_LIMIT_SMOOTHING);
    return previousLimitedSpeed;
}

void AudioEngine::setMaxPlatterSpeed(float newMax) {
    if (newMax < 0.1f) newMax = 0.1f; // clamp low
    if (newMax > 5.0f) newMax = 5.0f; // clamp high to prevent instability
    maxPlatterSpeed_.store(newMax);
    ALOGI("AudioEngine: maxPlatterSpeed updated to %.3f", newMax);
}

bool AudioEngine::tryBeginIntro() {
    bool expected = false;
    if (introSampleActive_.compare_exchange_strong(expected, true)) {
        return true; // We own intro playback
    }
    return false; // Intro already started
}

bool AudioEngine::init(AAssetManager* mgr) {
    ALOGI("AudioEngine init. this: %p", this);
    appAssetManager_ = mgr;
    oboe::AudioStreamBuilder builder;
    builder.setDirection(oboe::Direction::Output)
            ->setPerformanceMode(oboe::PerformanceMode::LowLatency)
            ->setSharingMode(oboe::SharingMode::Exclusive)
            ->setFormat(oboe::AudioFormat::Float)
            ->setChannelCount(oboe::ChannelCount::Stereo)
            ->setCallback(this);
    ALOGI("AudioEngine init: Explicitly setting sample rate to 48000 Hz for Oboe stream.");
    builder.setSampleRate(48000);
    ALOGI("AudioEngine init: Attempting to open stream...");
    oboe::Result result = builder.openStream(audioStream_);
    ALOGI("AudioEngine init: openStream result: %s. audioStream_.get() after open: %p",
          oboe::convertToText(result), (audioStream_ ? audioStream_.get() : nullptr) );
    if (result == oboe::Result::OK && audioStream_ && audioStream_.get() != nullptr) {
        streamSampleRate_ = audioStream_->getSampleRate();

        // Initialize master tick system with actual sample rate
        masterTickSystem_.setSampleRate(streamSampleRate_);
        ALOGI("MasterTickSystem initialized with sample rate: %u", streamSampleRate_);

        ALOGI("Stream opened successfully: SR=%u, Channels=%d, Format=%s, Current State: %s",
              streamSampleRate_, audioStream_->getChannelCount(),
              oboe::convertToText(audioStream_->getFormat()),
              oboe::convertToText(audioStream_->getState()));
        ALOGI("AudioEngine init: Stream opened. Active samples are initially null.");
        return true;
    } else {
        ALOGE("Failed to open stream OR stream object is invalid. Oboe Result: %s. audioStream_.get(): %p",
              oboe::convertToText(result), (audioStream_ ? audioStream_.get() : nullptr) );
        if (audioStream_ && audioStream_.get() != nullptr) { audioStream_->close(); }
        audioStream_.reset();
        return false;
    }
}

void AudioEngine::release() {
    ALOGI("AudioEngine release.");
    
    // Clean up VinylTracker first
    if (vinylTracker_) {
        vinylTracker_.reset(); // This will properly stop tracking and clean up
        ALOGI("AudioEngine release: VinylTracker cleaned up.");
    }
    
    if (audioStream_) {
        stopStream();
        audioStream_->close();
        audioStream_.reset();
    }
    {
        std::lock_guard<std::mutex> lock(cacheMutex_);
        activePlatterSample_.reset();
        activeMusicSample_.reset();
        platterSampleCache_.clear();
        platterSampleLru_.clear();
        musicTrackCache_.clear();
        musicTrackLru_.clear();
        ALOGI("AudioEngine release: Active samples reset and in-memory caches cleared.");
    }
    appAssetManager_ = nullptr;
}

oboe::Result AudioEngine::startStream() {
    ALOGI("AudioEngine::startStream() ENTRY. audioStream_.get(): %p", (audioStream_ ? audioStream_.get() : nullptr) );
    if (!audioStream_ || !audioStream_.get()) { ALOGE("Stream not initialized for startStream!"); return oboe::Result::ErrorNull; }
    oboe::StreamState currentState = audioStream_->getState();
    ALOGI("AudioEngine: Current stream state before requestStart: %s", oboe::convertToText(currentState));
    if (currentState == oboe::StreamState::Started || currentState == oboe::StreamState::Starting) { ALOGW("Stream already started/starting."); return oboe::Result::OK; }
    if (currentState == oboe::StreamState::Closed || currentState == oboe::StreamState::Disconnected) { ALOGE("Stream closed/disconnected (%s). Cannot start.", oboe::convertToText(currentState)); return oboe::Result::ErrorClosed; }
    oboe::Result result = audioStream_->requestStart();
    ALOGI("AudioEngine: audioStream_->requestStart() result: %s. State after: %s", oboe::convertToText(result), oboe::convertToText(audioStream_->getState()));
    return result;
}

oboe::Result AudioEngine::stopStream() {
    ALOGI("AudioEngine::stopStream() ENTRY. audioStream_.get(): %p", (audioStream_ ? audioStream_.get() : nullptr) );
    if (!audioStream_ || !audioStream_.get()) { ALOGE("Stream not initialized for stopStream!"); return oboe::Result::ErrorNull; }
    oboe::StreamState currentState = audioStream_->getState();
    ALOGI("AudioEngine: Current stream state before requestStop: %s", oboe::convertToText(currentState));
    if (currentState == oboe::StreamState::Stopped || currentState == oboe::StreamState::Stopping) { ALOGW("Stream already stopped/stopping."); return oboe::Result::OK; }
    if (currentState == oboe::StreamState::Closed || currentState == oboe::StreamState::Disconnected) { ALOGE("Stream closed/disconnected (%s). Cannot stop.", oboe::convertToText(currentState)); return oboe::Result::ErrorClosed; }
    oboe::Result result = audioStream_->requestStop();
    ALOGI("AudioEngine: audioStream_->requestStop() result: %s. State after: %s", oboe::convertToText(result), oboe::convertToText(audioStream_->getState()));
    return result;
}

void AudioEngine::stopMusicTrackInternal() {
    ALOGI("AudioEngine: stopMusicTrackInternal");
    if (activeMusicSample_) {
        activeMusicSample_->isPlaying.store(false);
        ALOGI("Stopped music track: %s", activeMusicSample_->filePath.c_str());
    } else {
        ALOGW("stopMusicTrackInternal: activeMusicSample_ is null.");
    }
}

void AudioEngine::setPlatterFaderVolumeInternal(float volume) {
    float clampedVolume = std::clamp(volume, 0.0f, 1.0f);
    platterFaderVolume_.store(clampedVolume);
    ALOGV("AudioEngine: Platter Fader Volume set to %f", clampedVolume);
}

void AudioEngine::setMusicMasterVolumeInternal(float volume) {
    float clampedVolume = std::clamp(volume, 0.0f, 1.0f);
    generalMusicVolume_.store(clampedVolume);
    ALOGI("AudioEngine: Music Master Volume set to %f", clampedVolume);
}

void AudioEngine::scratchPlatterActiveInternal(bool isActiveTouch, float angleDeltaOrRateFromViewModel) {
    // BACKSPIN DEBUG: Log audio positions before any changes
    if (activePlatterSample_ && activePlatterSample_->totalFrames > 0) {
        float currentAudioFrame = activePlatterSample_->preciseCurrentFrame.load();
        float currentAudioAngle = (currentAudioFrame / (float)activePlatterSample_->totalFrames) * 360.0f;
        while (currentAudioAngle >= 360.0f) currentAudioAngle -= 360.0f;
        while (currentAudioAngle < 0.0f) currentAudioAngle += 360.0f;
        
        uint64_t currentTick = masterTickSystem_.getCurrentTick();
        float tickBasedAngle = masterTickSystem_.ticksToVinylAngle(currentTick);
        
        ALOGI("BACKSPIN_DEBUG_AUDIO", "BEFORE_SCRATCH: isTouch=%d, angleDelta=%.2f°, audioFrame=%.1f, audioAngle=%.2f°, tickAngle=%.2f°", 
              isActiveTouch, angleDeltaOrRateFromViewModel, currentAudioFrame, currentAudioAngle, tickBasedAngle);
    }
    
    // OPTIMIZED: Commented out verbose scratch logging for performance
    // ALOGV_SCRATCH("AudioEngine::scratchPlatterActiveInternal - Input: isActiveTouch:%d, angleDeltaOrRate:%.4f", isActiveTouch, angleDeltaOrRateFromViewModel);

    // DEBUG: Track all calls to see if compensation is being overridden (reduced frequency)
    static int callCounter = 0;
    if (callCounter++ % 100 == 0) { // Log every 100th call instead of every call
        ALOGI("AudioEngine_Compensation: Call #%d - isTouch=%d, angleDelta=%.4f", callCounter, isActiveTouch, angleDeltaOrRateFromViewModel);
    }

    isFingerDownOnPlatter_.store(isActiveTouch);

    if (!activePlatterSample_ || activePlatterSample_->totalFrames == 0) {
        if(isActiveTouch) ALOGW("ScratchPlatterActive: Attempt on unloaded/invalid active platter sample.");
        if(activePlatterSample_) { activePlatterSample_->useEngineRateForPlayback_.store(false); }
        ALOGV_SCRATCH("AudioEngine::scratchPlatterActiveInternal - ActivePlatterSample State: useEngineRate:%d, isPlaying:%d",
              (activePlatterSample_ ? activePlatterSample_->useEngineRateForPlayback_.load() : -1),
              (activePlatterSample_ ? activePlatterSample_->isPlaying.load() : -1 ));
        return;
    }

    activePlatterSample_->useEngineRateForPlayback_.store(true);
    ALOGV_SCRATCH("AudioEngine::scratchPlatterActiveInternal - ActivePlatterSample State (after useEngineRate=true): useEngineRate:%d, isPlaying:%d", activePlatterSample_->useEngineRateForPlayback_.load(), activePlatterSample_->isPlaying.load());

    // SINGLE SOURCE OF TRUTH: Use SlipmatPhysics for all rate calculations

    if (isActiveTouch) {
        // Manual control - calculate rate and set SlipmatPhysics to manual mode
        float normalizedInputRate = 0.0f;
        
        if (std::fabs(angleDeltaOrRateFromViewModel) > 0.001f) {
            // FIXED: Touch input uses direct 1:1 mapping (not motor-speed normalized)
            // Touch represents user intention and should be immediately responsive
            normalizedInputRate = angleDeltaOrRateFromViewModel;  // Direct 1:1 mapping
        } else {
            // Below movement threshold
            normalizedInputRate = 0.0f;
        }

        float targetAudioRate = std::clamp(normalizedInputRate, -4.0f, 4.0f);

        // DEBUG: Track physics interruption and potential data gaps
        float currentPhysicsRate = slipmatPhysics_.getCurrentSpeed();
        float currentStoredRate = platterTargetPlaybackRate_.load();
        bool wasManualControl = slipmatPhysics_.isManualControl();

        // OPTIMIZED: Simplified rate flow logging for stability
        ALOGV_SCRATCH("RATE_FLOW_MANUAL: newRate=%.4f, angleDelta=%.4f", targetAudioRate, angleDeltaOrRateFromViewModel);

        if (!wasManualControl) {
            // Re-enable sync for smooth transitions
            callbackSyncData_.needsSync.store(true);
            ALOGI("MOTOR_TO_MANUAL_SYNC: Sync enabled for smooth transition");
        }

    // SIMPLIFIED: Queue manual control to be applied in audio callback
    float limitedManual = limitPlatterSpeed(targetAudioRate);
    pendingManualRate_.store(limitedManual);
    pendingSetManual_.store(true);

    // DIRECT AUDIO CONTROL: Set rate immediately during finger control (atomic, safe)
    platterTargetPlaybackRate_.store(limitedManual);

        if (!activePlatterSample_->isPlaying.load()) {
            activePlatterSample_->isPlaying.store(true);
        }

        ALOGV_SCRATCH("AudioEngine::scratchPlatterActiveInternal - Manual control: rate=%.4f", targetAudioRate);
    } else {
        // Motor control - this is called during coasting (finger released but still moving)
        // We need to preserve the current speed and let physics handle the transition
        float currentRate = angleDeltaOrRateFromViewModel; // This is the coasting rate from ViewModel
        float targetMotorSpeed = 1.0f; // Normal motor speed (25 RPM)

    // CRITICAL: Queue motor control update to be applied in audio callback
    pendingMotorCurrent_.store(currentRate);
    pendingMotorTarget_.store(targetMotorSpeed);
    pendingSetMotor_.store(true);

        // Don't set platterTargetPlaybackRate_ here - let audio callback handle it with physics

        ALOGI("AudioEngine::scratchPlatterActiveInternal - Motor control: current=%.4f, target=%.4f, physics will handle transition", currentRate, targetMotorSpeed);
    }

    ALOGV_SCRATCH("AudioEngine::scratchPlatterActiveInternal - Final ActivePlatterSample State: useEngineRate:%d, isPlaying:%d", activePlatterSample_->useEngineRateForPlayback_.load(), activePlatterSample_->isPlaying.load());
    
    // BACKSPIN DEBUG: Log audio positions after changes
    if (activePlatterSample_ && activePlatterSample_->totalFrames > 0) {
        float afterAudioFrame = activePlatterSample_->preciseCurrentFrame.load();
        float afterAudioAngle = (afterAudioFrame / (float)activePlatterSample_->totalFrames) * 360.0f;
        while (afterAudioAngle >= 360.0f) afterAudioAngle -= 360.0f;
        while (afterAudioAngle < 0.0f) afterAudioAngle += 360.0f;
        
        uint64_t afterTick = masterTickSystem_.getCurrentTick();
        float afterTickBasedAngle = masterTickSystem_.ticksToVinylAngle(afterTick);
        
        ALOGI("BACKSPIN_DEBUG_AUDIO", "AFTER_SCRATCH: audioFrame=%.1f, audioAngle=%.2f°, tickAngle=%.2f°", 
              afterAudioFrame, afterAudioAngle, afterTickBasedAngle);
    }
}

void AudioEngine::releasePlatterTouchInternal() {
    ALOGI("AudioEngine: releasePlatterTouchInternal - SMART UNIFIED SYNC POINT");
    isFingerDownOnPlatter_.store(false);

    if (activePlatterSample_) {
        // LOG CURRENT STATE before sync request
        float currentAudioFrame = activePlatterSample_->preciseCurrentFrame.load();
        uint64_t currentTick = masterTickSystem_.getCurrentTick();
        float totalFrames = (float)activePlatterSample_->totalFrames;
        float audioProgress = (totalFrames > 0) ? (currentAudioFrame / totalFrames) : 0.0f;
        
        ALOGI("TOUCH_RELEASE_STATE: AudioFrame=%.1f/%.1f (%.2f%%), CurrentTick=%" PRIu64 "",
              currentAudioFrame, totalFrames, audioProgress * 100.0f, currentTick);

        // ===== TRIGGER SMART UNIFIED SYNC =====
        // Request immediate sync in audio callback - this will do validated direct master tick reset
        callbackSyncData_.needsSync.store(true);
        
        ALOGI("SMART_UNIFIED_SYNC: Sync requested on touch release - audio callback will validate and reset if needed");

        activePlatterSample_->useEngineRateForPlayback_.store(true);

        // CRITICAL: Switch SlipmatPhysics from manual to motor control
        // Get the ACTUAL current playback rate, not the stored speed
        float currentActualRate = platterTargetPlaybackRate_.load();
        float targetMotorSpeed = 1.0f; // Normal 25 RPM motor speed

    // IMPORTANT: Queue transition from actual current rate to motor target on audio thread
    pendingMotorCurrent_.store(currentActualRate);
    pendingMotorTarget_.store(targetMotorSpeed);
    pendingSetMotor_.store(true);

    ALOGI("AudioEngine: Finger released. SlipmatPhysics transitioning from ACTUAL RATE %.4f to %.4f", currentActualRate, targetMotorSpeed);
    // Engage a short holdoff (~20ms) to prevent immediate motor positioning while volumes/rates settle
    motorPositionHoldoffFrames_.store(960); // ~20ms at 48kHz
    }
}


void AudioEngine::setDegreesPerFrameForUnityRateInternal(float degrees) {
    if (degrees > 0.0f) {
        degreesPerFrameForUnityRate_ = degrees;
        ALOGI("AudioEngine: degreesPerFrameForUnityRate_ set to %.4f", degreesPerFrameForUnityRate_);
    } else {
        ALOGE("AudioEngine: Invalid degreesPerFrameForUnityRate_ value: %.4f", degrees);
    }
}

void AudioEngine::engineLoadUserPlatterSample(const std::string& uriString, int fd, long offset, long length, uint64_t modificationTimestamp) {
    std::lock_guard<std::mutex> lock(cacheMutex_);
    std::string inMemoryCacheKey = uriString; 

    if (platterSampleCache_.count(inMemoryCacheKey)) {
        auto cachedSample = platterSampleCache_[inMemoryCacheKey];
        activePlatterSample_ = cachedSample;
        updateLru(platterSampleLru_, inMemoryCacheKey);
        ALOGI("AudioEngine: Platter sample '%s' (ModTime: %" PRIu64 ") found in IN-MEMORY cache.", uriString.c_str(), modificationTimestamp);
        if (activePlatterSample_) {
            activePlatterSample_->preciseCurrentFrame.store(0.0f);
            activePlatterSample_->currentTick.store(0);
            activePlatterSample_->isPlaying.store(true);
            activePlatterSample_->loop.store(true);
            activePlatterSample_->useEngineRateForPlayback_.store(true);
            activePlatterSample_->playOnceThenLoopSilently = false;
            activePlatterSample_->playedOnce = false;
            setupTicksPerVinylRotation(); // Setup tick system for new sample
        }
        return;
    }
    ALOGI("AudioEngine: Platter sample '%s' (ModTime: %" PRIu64 ") not in IN-MEMORY cache. Attempting to load (FD or persistent cache).", uriString.c_str(), modificationTimestamp);

    // Validate file descriptor
    if (fd < 0) {
        ALOGE("AudioEngine: Invalid FD (%d) for platter sample %s", fd, uriString.c_str());
        return;
    }
    if (length <= 0) {
        ALOGE("AudioEngine: Invalid length (%ld) for platter sample %s", length, uriString.c_str());
        return;
    }
    // Try fstat to check if fd is open and valid
    struct stat fdStat;
    if (fstat(fd, &fdStat) != 0) {
        ALOGE("AudioEngine: fstat failed for FD %d (platter sample %s): %s", fd, uriString.c_str(), strerror(errno));
        return;
    }
    ALOGI("AudioEngine: FD %d for platter sample %s is valid. File size: %lld", fd, uriString.c_str(), (long long)fdStat.st_size);
    std::vector<unsigned char> buffer(length);
    off_t lseek_result = lseek(fd, offset, SEEK_SET);
    if (lseek_result == -1) { ALOGE("AudioEngine: Failed to lseek for platter sample '%s'. Error: %s", uriString.c_str(), strerror(errno)); return; }
    ssize_t bytesRead = read(fd, buffer.data(), length);
    if (bytesRead <= 0) { ALOGE("AudioEngine: Failed to read or read 0 bytes for platter sample '%s'. Error: %s", uriString.c_str(), strerror(errno)); return; }
    if (bytesRead != length) { ALOGW("AudioEngine: Read fewer bytes (%zd) than expected (%ld) for platter sample '%s'.", bytesRead, length, uriString.c_str()); buffer.resize(bytesRead); }
    ALOGI("AudioEngine: Successfully read %zd bytes for platter sample '%s'.", bytesRead, uriString.c_str());

    auto newSample = std::make_shared<AudioSample>();
    newSample->audioEnginePtr = this;
    newSample->loadFromMemory(buffer.data(), buffer.size(), uriString, modificationTimestamp);

    if (newSample->totalFrames > 0) {
        ALOGI("AudioEngine: User platter sample '%s' (ModTime: %" PRIu64 ") loaded/decoded successfully.", uriString.c_str(), modificationTimestamp);
        newSample->loop.store(true);
        newSample->playOnceThenLoopSilently = false;
        newSample->preciseCurrentFrame.store(0.0f);
        newSample->currentTick.store(0);
        newSample->isPlaying.store(true);
        newSample->useEngineRateForPlayback_.store(true);
        activePlatterSample_ = newSample;
        setupTicksPerVinylRotation(); // Setup tick system for new sample
        manageCache(platterSampleCache_, platterSampleLru_, inMemoryCacheKey, newSample);
    } else {
        ALOGE("AudioEngine: Failed to load/decode user platter sample '%s' (ModTime: %" PRIu64 ").", uriString.c_str(), modificationTimestamp);
    }
}

void AudioEngine::engineLoadUserMusicTrack(const std::string& uriString, int fd, long offset, long length, uint64_t modificationTimestamp) {
    std::lock_guard<std::mutex> lock(cacheMutex_);
    std::string inMemoryCacheKey = uriString;

    if (musicTrackCache_.count(inMemoryCacheKey)) {
        activeMusicSample_ = musicTrackCache_[inMemoryCacheKey];
        updateLru(musicTrackLru_, inMemoryCacheKey);
        ALOGI("AudioEngine: Music track '%s' (ModTime: %" PRIu64 ") found in IN-MEMORY cache.", uriString.c_str(), modificationTimestamp);
        if (activeMusicSample_) {
            activeMusicSample_->preciseCurrentFrame.store(0.0f);
            activeMusicSample_->isPlaying.store(true);
            activeMusicSample_->loop.store(false);
            activeMusicSample_->useEngineRateForPlayback_.store(false);
        }
        return;
    }
    ALOGI("AudioEngine: Music track '%s' (ModTime: %" PRIu64 ") not in IN-MEMORY cache. Attempting to load (FD or persistent cache).", uriString.c_str(), modificationTimestamp);

    // Validate file descriptor
    if (fd < 0) {
        ALOGE("AudioEngine: Invalid FD (%d) for music track %s", fd, uriString.c_str());
        return;
    }
    if (length <= 0) {
        ALOGE("AudioEngine: Invalid length (%ld) for music track %s", length, uriString.c_str());
        return;
    }
    // Try fstat to check if fd is open and valid
    struct stat fdStat;
    if (fstat(fd, &fdStat) != 0) {
        ALOGE("AudioEngine: fstat failed for FD %d (music track %s): %s", fd, uriString.c_str(), strerror(errno));
        return;
    }
    ALOGI("AudioEngine: FD %d for music track %s is valid. File size: %lld", fd, uriString.c_str(), (long long)fdStat.st_size);
    std::vector<unsigned char> buffer(length);
    off_t lseek_result = lseek(fd, offset, SEEK_SET);
    if (lseek_result == -1) { ALOGE("AudioEngine: Failed to lseek for music track '%s'. Error: %s", uriString.c_str(), strerror(errno)); return; }
    ssize_t bytesRead = read(fd, buffer.data(), length);
    if (bytesRead <= 0) { ALOGE("AudioEngine: Failed to read or read 0 bytes for music track '%s'. Error: %s", uriString.c_str(), strerror(errno)); return; }
    if (bytesRead != length) { ALOGW("AudioEngine: Read fewer bytes (%zd) than expected (%ld) for music track '%s'.", bytesRead, length, uriString.c_str()); buffer.resize(bytesRead); }
    ALOGI("AudioEngine: Successfully read %zd bytes for music track '%s'.", bytesRead, uriString.c_str());

    auto newSample = std::make_shared<AudioSample>();
    newSample->audioEnginePtr = this;
    newSample->loadFromMemory(buffer.data(), buffer.size(), uriString, modificationTimestamp);

    if (newSample->totalFrames > 0) {
        ALOGI("AudioEngine: User music track '%s' (ModTime: %" PRIu64 ") loaded/decoded successfully.", uriString.c_str(), modificationTimestamp);
        newSample->loop.store(false);
        newSample->playOnceThenLoopSilently = false;
        newSample->preciseCurrentFrame.store(0.0f);
        newSample->isPlaying.store(true);
        newSample->useEngineRateForPlayback_.store(false);
        newSample->isMusicTrack = true; // Mark as music track for callback
        activeMusicSample_ = newSample;
        manageCache(musicTrackCache_, musicTrackLru_, inMemoryCacheKey, newSample);
    } else {
        ALOGE("AudioEngine: Failed to load/decode user music track '%s' (ModTime: %" PRIu64 ").", uriString.c_str(), modificationTimestamp);
    }
}

void AudioEngine::engineLoadAssetPlatter(const std::string& assetPath, uint64_t appVersionCode) {
    std::lock_guard<std::mutex> lock(cacheMutex_);
    if (!appAssetManager_) { ALOGE("AudioEngine: Asset manager not available for loading %s.", assetPath.c_str()); return; }
    std::string inMemoryCacheKey = assetPath + "_" + std::to_string(appVersionCode);

    if (platterSampleCache_.count(inMemoryCacheKey)) {
        activePlatterSample_ = platterSampleCache_[inMemoryCacheKey];
        updateLru(platterSampleLru_, inMemoryCacheKey);
        ALOGI("AudioEngine: Asset platter sample '%s' (AppVer: %" PRIu64 ") found in IN-MEMORY cache.", assetPath.c_str(), appVersionCode);
        if (activePlatterSample_) {
            activePlatterSample_->preciseCurrentFrame.store(0.0f);
            activePlatterSample_->currentTick.store(0);
            activePlatterSample_->isPlaying.store(true);
            activePlatterSample_->loop.store(true);
            activePlatterSample_->useEngineRateForPlayback_.store(true);
            activePlatterSample_->playOnceThenLoopSilently = false;
            activePlatterSample_->playedOnce = false;
            setupTicksPerVinylRotation(); // Setup tick system for cached sample
        }
        return;
    }
    ALOGI("AudioEngine: Asset platter sample '%s' (AppVer: %" PRIu64 ") not in IN-MEMORY cache. Loading.", assetPath.c_str(), appVersionCode);
    auto newSample = std::make_shared<AudioSample>();
    newSample->audioEnginePtr = this;
    newSample->load(this->appAssetManager_, assetPath, this, appVersionCode);
    if (newSample->totalFrames > 0) {
        newSample->loop.store(true);
        newSample->playOnceThenLoopSilently = false;
        newSample->preciseCurrentFrame.store(0.0f);
        newSample->currentTick.store(0);
        newSample->isPlaying.store(true);
        newSample->useEngineRateForPlayback_.store(true);
        activePlatterSample_ = newSample;
        setupTicksPerVinylRotation(); // Setup tick system for new asset sample
        manageCache(platterSampleCache_, platterSampleLru_, inMemoryCacheKey, newSample);
    } else {
        ALOGE("AudioEngine: Failed to load asset platter sample '%s' (AppVer: %" PRIu64 ").", assetPath.c_str(), appVersionCode);
    }
}

void AudioEngine::engineLoadAssetMusic(const std::string& assetPath, uint64_t appVersionCode) {
    std::lock_guard<std::mutex> lock(cacheMutex_);
    if (!appAssetManager_) { ALOGE("AudioEngine: Asset manager not available for loading %s.", assetPath.c_str()); return; }
    std::string inMemoryCacheKey = assetPath + "_" + std::to_string(appVersionCode);

    if (musicTrackCache_.count(inMemoryCacheKey)) {
        activeMusicSample_ = musicTrackCache_[inMemoryCacheKey];
        updateLru(musicTrackLru_, inMemoryCacheKey);
        ALOGI("AudioEngine: Asset music track '%s' (AppVer: %" PRIu64 ") found in IN-MEMORY cache.", assetPath.c_str(), appVersionCode);
        if (activeMusicSample_) {
            activeMusicSample_->preciseCurrentFrame.store(0.0f);
            activeMusicSample_->isPlaying.store(true);
            activeMusicSample_->loop.store(false);
            activeMusicSample_->useEngineRateForPlayback_.store(false);
        }
        return;
    }
    ALOGI("AudioEngine: Asset music track '%s' (AppVer: %" PRIu64 ") not in IN-MEMORY cache. Loading.", assetPath.c_str(), appVersionCode);
    auto newSample = std::make_shared<AudioSample>();
    newSample->audioEnginePtr = this;
    newSample->load(this->appAssetManager_, assetPath, this, appVersionCode);
    if (newSample->totalFrames > 0) {
        newSample->loop.store(false);
        newSample->playOnceThenLoopSilently = false;
        newSample->preciseCurrentFrame.store(0.0f);
        newSample->isPlaying.store(true);
        newSample->useEngineRateForPlayback_.store(false);
        newSample->isMusicTrack = true; // Mark as music track for callback
        activeMusicSample_ = newSample;
        manageCache(musicTrackCache_, musicTrackLru_, inMemoryCacheKey, newSample);
    } else {
        ALOGE("AudioEngine: Failed to load asset music track '%s' (AppVer: %" PRIu64 ").", assetPath.c_str(), appVersionCode);
    }
}

void AudioEngine::enginePlayIntroAndLoopOnPlatter(const std::string& assetPath, uint64_t appVersionCode) {
    std::lock_guard<std::mutex> lock(cacheMutex_);
    if (!appAssetManager_) { ALOGE("AudioEngine: Asset manager not available for intro %s.", assetPath.c_str()); return; }
    ALOGI("AudioEngine: enginePlayIntroAndLoopOnPlatter with ASSET path: %s, AppVer: %" PRIu64 "", assetPath.c_str(), appVersionCode);
    if (!tryBeginIntro()) {
        ALOGW("AudioEngine: Intro playback request ignored (already active). Path: %s", assetPath.c_str());
        return;
    }
    std::string inMemoryCacheKey = assetPath + "_intro_" + std::to_string(appVersionCode);

    if (platterSampleCache_.count(inMemoryCacheKey)) {
        activePlatterSample_ = platterSampleCache_[inMemoryCacheKey];
        setupTicksPerVinylRotation(); // Setup tick system for cached intro sample
        updateLru(platterSampleLru_, inMemoryCacheKey);
        ALOGI("AudioEngine: Intro asset '%s' (AppVer: %" PRIu64 ") found in IN-MEMORY cache.", assetPath.c_str(), appVersionCode);
    } else {
        ALOGI("AudioEngine: Intro asset '%s' (AppVer: %" PRIu64 ") not in IN-MEMORY cache. Loading.", assetPath.c_str(), appVersionCode);
        auto newSample = std::make_shared<AudioSample>();
        newSample->audioEnginePtr = this;
        newSample->load(this->appAssetManager_, assetPath, this, appVersionCode);
        if (newSample->totalFrames > 0) {
            activePlatterSample_ = newSample;
            setupTicksPerVinylRotation(); // Setup tick system for intro sample
            manageCache(platterSampleCache_, platterSampleLru_, inMemoryCacheKey, newSample);
        } else {
            ALOGE("Failed to load intro ASSET sample from path: %s (AppVer: %" PRIu64 ")", assetPath.c_str(), appVersionCode);
            return;
        }
    }

    if (activePlatterSample_ && activePlatterSample_->totalFrames > 0) {
    activePlatterSample_->playOnceThenLoopSilently = true;
        activePlatterSample_->playedOnce = false;
        activePlatterSample_->loop.store(false);
        activePlatterSample_->preciseCurrentFrame.store(0.0f);
        activePlatterSample_->currentTick.store(0);
        activePlatterSample_->isPlaying.store(true);
        activePlatterSample_->useEngineRateForPlayback_.store(false);
        platterTargetPlaybackRate_.store(1.0f);
        ALOGI("Intro ASSET sample '%s' set up. Will play once then loop silently.", activePlatterSample_->filePath.c_str());
    } else {
        ALOGE("Failed to set up intro ASSET sample from path: %s (already logged error or sample is null)", assetPath.c_str());
    }
}

oboe::DataCallbackResult AudioEngine::onAudioReady(oboe::AudioStream* stream, void* audioData, int32_t numFrames) {
    // Increment master tick counter - this is our most precise timing source
    masterTickSystem_.incrementTick(numFrames);

    // DIRECT CALLBACK SYNC: Calculate baseTickOffset directly in audio callback
    // This eliminates ALL timing gaps for perfect synchronization
    // SMARTER SYNC: Only sync at critical transition points, NOT during active touch
    bool isManualControl = slipmatPhysics_.isManualControl();
    bool isFingerDown = isFingerDownOnPlatter_.load();
    bool needsSync = callbackSyncData_.needsSync.load();

    // Apply any pending control updates from UI threads (lock-free)
    // Ensures SlipmatPhysics mutations happen only on the audio thread
    if (pendingSetManual_.exchange(false)) {
        float uiRate = pendingManualRate_.load();
        float limited = limitPlatterSpeed(uiRate);
        slipmatPhysics_.setManualControl(true, limited);
        platterTargetPlaybackRate_.store(limited);
    }
    if (pendingSetMotor_.exchange(false)) {
        float cur = pendingMotorCurrent_.load();
        float tgt = pendingMotorTarget_.load();
        slipmatPhysics_.setCurrentSpeed(cur);
        slipmatPhysics_.setManualControl(false);
        slipmatPhysics_.setTargetSpeed(tgt);
    }

    // Note: Removed verbose CONTROL_STATE_DEBUG logging for performance

    // ENHANCED DUAL-MODE SYNC: Full sync when released, micro-sync during touch
    if (needsSync && activePlatterSample_ && !isFingerDown) {
        // FULL SYNC MODE: Complete sync when finger is released
        // Sample both timing systems at the exact same moment (highest precision)
        uint64_t currentTick = masterTickSystem_.getCurrentTick();
        float currentFrame = activePlatterSample_->preciseCurrentFrame.load();
        float totalFrames = static_cast<float>(activePlatterSample_->totalFrames);

        // SMART UNIFIED SYNC: DIRECT MASTER TICK RESET with validation
        if (totalFrames > 0) {
            float audioProgress = currentFrame / totalFrames;
            // Handle looping - get position within current rotation
            audioProgress = fmodf(audioProgress, 1.0f);
            if (audioProgress < 0.0f) audioProgress += 1.0f;
            
            uint64_t ticksPerRotation = masterTickSystem_.getTicksPerVinylRotation();
            uint64_t targetTickInRotation = static_cast<uint64_t>(audioProgress * ticksPerRotation);
            
            // VALIDATION: Only sync if position difference is significant (prevents unnecessary resets)
            uint64_t currentTickInRotation = currentTick % ticksPerRotation;
            uint64_t tickDifference = (targetTickInRotation > currentTickInRotation) ? 
                (targetTickInRotation - currentTickInRotation) : 
                (currentTickInRotation - targetTickInRotation);
            
            // Only sync if difference is >1% of rotation (ultra-tight threshold for maximum accuracy)
            uint64_t syncThreshold = ticksPerRotation / 100; // 1% threshold for immediate drift correction
            
            if (tickDifference > syncThreshold) {
                // CRITICAL: DIRECT RESET - no offset calculations, just set tick to match audio exactly
                masterTickSystem_.reset();
                masterTickSystem_.incrementTick(targetTickInRotation);
                
                ALOGI("SMART_UNIFIED_SYNC: Frame=%.8f/%.1f (%.3f%%) → TickReset=%" PRIu64 " (diff=%" PRIu64 " > threshold=%" PRIu64 ")",
                      currentFrame, totalFrames, audioProgress * 100.0f, targetTickInRotation, tickDifference, syncThreshold);
            } else {
                ALOGI("SMART_UNIFIED_SYNC: SKIPPED - TickDiff=%" PRIu64 " < threshold=%" PRIu64 " (%.1f%% of rotation)",
                      tickDifference, syncThreshold, (float)tickDifference * 100.0f / ticksPerRotation);
            }
            
            // Mark that we've performed a direct sync (disable CallbackSync data)
            callbackSyncData_.masterTick.store(0);  // Zero indicates direct sync was used
            callbackSyncData_.audioFrame.store(currentFrame);
            callbackSyncData_.dataReady.store(false);  // Disable CallbackSync
        } else {
            ALOGW("SMART_UNIFIED_SYNC: No totalFrames available for sync");
            callbackSyncData_.dataReady.store(false);
        }

        callbackSyncData_.needsSync.store(false);
    } else if (needsSync && isFingerDown) {
        // MICRO-SYNC MODE: Gentle drift correction during touch to prevent accumulation
        uint64_t currentTick = masterTickSystem_.getCurrentTick();
        float currentFrame = activePlatterSample_->preciseCurrentFrame.load();
        float totalFrames = static_cast<float>(activePlatterSample_->totalFrames);

        if (totalFrames > 0) {
            float audioProgress = currentFrame / totalFrames;
            audioProgress = fmodf(audioProgress, 1.0f);
            if (audioProgress < 0.0f) audioProgress += 1.0f;
            
            uint64_t ticksPerRotation = masterTickSystem_.getTicksPerVinylRotation();
            uint64_t targetTickInRotation = static_cast<uint64_t>(audioProgress * ticksPerRotation);
            uint64_t currentTickInRotation = currentTick % ticksPerRotation;
            uint64_t tickDifference = (targetTickInRotation > currentTickInRotation) ? 
                (targetTickInRotation - currentTickInRotation) : 
                (currentTickInRotation - targetTickInRotation);
            
            // MICRO-SYNC: Only if drift is >5% (larger threshold during touch to avoid position jumps)
            uint64_t microSyncThreshold = ticksPerRotation / 20; // 5% threshold during touch
            
            if (tickDifference > microSyncThreshold) {
                // GENTLE CORRECTION: Small incremental adjustment instead of full reset
                uint64_t correctionAmount = tickDifference / 4; // Correct 25% of the drift
                if (targetTickInRotation > currentTickInRotation) {
                    masterTickSystem_.incrementTick(correctionAmount);
                } else {
                    // Slight delay in audio progression instead of rewinding
                    // (Less jarring than backward tick adjustment)
                }
                
                ALOGI("MICRO_SYNC: During touch - corrected %" PRIu64 " ticks (25%% of %" PRIu64 " tick drift)", 
                      correctionAmount, tickDifference);
            } else {
                ALOGI("SMART_UNIFIED_SYNC: BLOCKED during active touch - drift %" PRIu64 " < threshold %" PRIu64, 
                      tickDifference, microSyncThreshold);
            }
        }
        // Keep needsSync true so full sync triggers after finger release
    } else if (needsSync) {
        // CRITICAL FIX: CallbackSync was completely disabled for testing
        callbackSyncData_.needsSync.store(false);
        static int disabledLogCounter = 0;
        if (disabledLogCounter++ % 100 == 0) {
            ALOGI("CALLBACKSYNC_DISABLED: Completely disabled for testing position jumps");
        }
    }
    
    // OPTIMIZED: Reduced buffer logging frequency for performance
    static int bufferLogCounter = 0;
    if (bufferLogCounter++ % 24000 == 0) { // Every ~500ms instead of ~1s
        float bufferTimeMs = (float)numFrames / 48000.0f * 1000.0f;
        ALOGI("AudioBuffer: %d frames, %.2fms", numFrames, bufferTimeMs);
    }

    auto* outputBuffer = static_cast<float*>(audioData);
    const int32_t channelCount = stream->getChannelCount();
    memset(outputBuffer, 0, static_cast<size_t>(numFrames) * channelCount * sizeof(float));

    // Get current target volumes
    float targetPlatterVol = platterFaderVolume_.load();
    float targetMusicVol = generalMusicVolume_.load();
    
    // Check for volume changes and apply minimal smoothing only when needed
    float platterVol = targetPlatterVol;
    float musicVol = targetMusicVol;
    
    // Apply single-sample smoothing only if volume changed significantly (>1% difference)
    if (std::abs(targetPlatterVol - lastPlatterVolume_) > 0.01f) {
        platterVol = lastPlatterVolume_ * 0.5f + targetPlatterVol * 0.5f; // 50/50 blend for immediate response
        // Engage holdoff only on true cuts and when finger is NOT down to avoid touch lockouts
        bool fingerNow = isFingerDownOnPlatter_.load();
        float delta = std::abs(targetPlatterVol - lastPlatterVolume_);
        bool nearZeroCross = (targetPlatterVol < 0.05f || lastPlatterVolume_ < 0.05f);
        bool bigMove = delta > 0.25f; // treat >25% change as a cut
        if (!fingerNow && (bigMove || nearZeroCross)) {
            int frames = std::max(numFrames, 480); // ~10ms minimum
            faderActivityHoldoffFrames_.store(frames);
        }
    }
    if (std::abs(targetMusicVol - lastMusicVolume_) > 0.01f) {
        musicVol = lastMusicVolume_ * 0.5f + targetMusicVol * 0.5f; // 50/50 blend for immediate response
    }
    
    lastPlatterVolume_ = platterVol;
    lastMusicVolume_ = musicVol;

    // SINGLE SOURCE OF TRUTH: Calculate SlipmatPhysics at audio rate (48kHz)
    // This runs every audio callback for ultra-smooth motor transitions
    if (activePlatterSample_ && !slipmatPhysics_.isManualControl()) {
        // Only calculate physics during motor control (not manual scratching)
        float physicsRate = slipmatPhysics_.calculateNextSpeed();
        float limitedRate = limitPlatterSpeed(physicsRate);
        platterTargetPlaybackRate_.store(limitedRate);

        // ENHANCED: More frequent logging to track rate flow
        static int physicsLogCounter = 0;
        if (physicsLogCounter++ % 480 == 0) { // Every ~10ms at 48kHz for detailed tracking
            ALOGE("RATE_FLOW_PHYSICS: current=%.4f, target=%.4f, limited=%.4f, manual=%d",
                  physicsRate, slipmatPhysics_.getTargetSpeed(), limitedRate, slipmatPhysics_.isManualControl());
        }
    } else if (activePlatterSample_) {
        // DEBUG: Log when physics is bypassed due to manual control
        static int manualLogCounter = 0;
        if (manualLogCounter++ % 480 == 0) { // Every ~10ms at 48kHz
            ALOGE("MANUAL_BYPASS: Physics bypassed, manual=%d, storedRate=%.4f",
                  slipmatPhysics_.isManualControl(), platterTargetPlaybackRate_.load());
        }
    } else {
        // OPTIMIZED: Reduced inactive physics logging
        static int noPhysicsLogCounter = 0;
        if (noPhysicsLogCounter++ % 48000 == 0) { // Every ~1s at 48kHz (reduced from 100ms)
            ALOGI("SlipmatPhysics INACTIVE: sample=%d, manual=%d",
                  (activePlatterSample_ != nullptr), slipmatPhysics_.isManualControl());
        }
    }

    // Decrement holdoff counters
    int holdoffDec = numFrames;
    int currentMotorHoldoff = motorPositionHoldoffFrames_.load();
    if (currentMotorHoldoff > 0) {
        motorPositionHoldoffFrames_.store(std::max(0, currentMotorHoldoff - holdoffDec));
    }
    int currentFaderHoldoff = faderActivityHoldoffFrames_.load();
    if (currentFaderHoldoff > 0) {
        faderActivityHoldoffFrames_.store(std::max(0, currentFaderHoldoff - holdoffDec));
    }

    // ===== CRITICAL: MOTOR CONTROL POSITIONING IMPLEMENTATION =====
    // This is the MISSING piece that ensures perfect sync during motor control
    if (activePlatterSample_) {
        uint64_t currentTick = masterTickSystem_.getCurrentTick();
        uint64_t ticksPerRotation = masterTickSystem_.getTicksPerVinylRotation();
        bool isFingerDown = isFingerDownOnPlatter_.load();
        bool useEngineRate = activePlatterSample_->useEngineRateForPlayback_.load();
        
        // CRITICAL FIX: Check if SlipmatPhysics is transitioning to avoid position conflicts
        float currentPhysicsSpeed = slipmatPhysics_.getCurrentSpeed();
        float targetPhysicsSpeed = slipmatPhysics_.getTargetSpeed();
        bool isPhysicsTransitioning = abs(currentPhysicsSpeed - targetPhysicsSpeed) > 0.01f; // 1% threshold

        // MOTOR CONTROL MODE: Use tick-based positioning for perfect motor sync
        // CRITICAL: Only when NOT under finger control AND NOT during physics transitions
        // FIXED: Improved position calculation logic
    bool motorHoldoffActive = (motorPositionHoldoffFrames_.load() > 0);
        if (ticksPerRotation > 0 && activePlatterSample_->totalFrames > 0 &&
            !isFingerDown && useEngineRate && !isPhysicsTransitioning && !motorHoldoffActive) {

            // Calculate exact audio frame position from master tick
            uint64_t ticksInCurrentRotation = currentTick % ticksPerRotation;
            float exactAudioFrame = ((float)ticksInCurrentRotation / (float)ticksPerRotation) * (float)activePlatterSample_->totalFrames;

            // IMPROVED SAFEGUARD: Get current position and calculate difference more carefully
            float currentAudioFrame = activePlatterSample_->preciseCurrentFrame.load();
            
            // Handle wrap-around: if exactAudioFrame is near beginning and currentFrame is near end, or vice versa
            float positionDiff = exactAudioFrame - currentAudioFrame;
            float totalFrames = (float)activePlatterSample_->totalFrames;
            
            // Check for wrap-around scenarios
            if (positionDiff > totalFrames * 0.5f) {
                // Current is near beginning, exact is near end - wrap backwards
                positionDiff -= totalFrames;
            } else if (positionDiff < -totalFrames * 0.5f) {
                // Current is near end, exact is near beginning - wrap forwards  
                positionDiff += totalFrames;
            }
            
            float absoluteDiff = abs(positionDiff);
            
            // INTELLIGENT APPROACH: Allow reasonable corrections but let auto-calibration handle large drift
            // Small corrections (< 1000 frames = ~20ms): Motor positioning handles these immediately
            // Medium corrections (1000-5000 frames): Gradual motor positioning
            // Large corrections (> 5000 frames): Let auto-calibration handle, skip motor positioning
            float maxImmediateJump = (float)numFrames * 2.0f; // ~4ms for immediate correction
            float maxGradualJump = 1000.0f; // ~20ms for gradual correction
            float maxMotorJump = 5000.0f; // ~100ms - beyond this, let auto-calibration handle
            
            if (absoluteDiff <= maxImmediateJump) {
                // IMMEDIATE CORRECTION: Apply position update for small differences
                static int updateCounter = 0;
                if (updateCounter++ % 10 == 0) { // Only update every 10th callback (~1ms intervals)
                    activePlatterSample_->preciseCurrentFrame.store(exactAudioFrame);
                    
                    // DEBUG: Log motor control positioning (throttled)
                    static int motorPositionLogCounter = 0;
                    if (motorPositionLogCounter++ % 480 == 0) { // Every ~10ms at 48kHz
                        ALOGI("MOTOR_POSITION_IMMEDIATE: Frame=%.1f→%.1f, Diff=%.1f",
                              currentAudioFrame, exactAudioFrame, absoluteDiff);
                    }
                }
            } else if (absoluteDiff <= maxGradualJump) {
                // GRADUAL CORRECTION: Apply slower corrections for medium differences
                static int gradualCounter = 0;
                if (gradualCounter++ % 50 == 0) { // Only update every 50th callback (~5ms intervals)
                    // Apply partial correction toward target
                    float correctionStep = positionDiff * 0.1f; // 10% of the difference
                    float newFrame = currentAudioFrame + correctionStep;
                    activePlatterSample_->preciseCurrentFrame.store(newFrame);
                    
                    static int gradualLogCounter = 0;
                    if (gradualLogCounter++ % 100 == 0) {
                        ALOGI("MOTOR_POSITION_GRADUAL: Frame=%.1f→%.1f, Remaining=%.1f",
                              currentAudioFrame, newFrame, absoluteDiff - abs(correctionStep));
                    }
                }
            } else if (absoluteDiff <= maxMotorJump) {
                // DEFER TO AUTO-CALIBRATION: Log that we're letting auto-calibration handle this
                static int deferLogCounter = 0;
                if (deferLogCounter++ % 200 == 0) {
                    ALOGI("MOTOR_POSITION_DEFERRED: Diff=%.1f - letting auto-calibration handle large correction",
                          absoluteDiff);
                }
            } else {
                // PROTECTION: Reject massive position jumps that indicate corruption
                static int rejectLogCounter = 0;
                if (rejectLogCounter++ % 100 == 0) { // Reduced log frequency
                    ALOGE("MOTOR_POSITION_REJECTED: Diff=%.1f > MaxMotor=%.1f - protecting from corruption",
                          absoluteDiff, maxMotorJump);
                }
            }
    } else if (isPhysicsTransitioning || motorHoldoffActive) {
            // DEBUG: Log when motor positioning is disabled due to physics transition
            static int transitionLogCounter = 0;
            if (transitionLogCounter++ % 480 == 0) { // Every ~10ms
        if (motorHoldoffActive) {
                    ALOGI("MOTOR_POSITION_DISABLED: Holdoff active (motor=%d, fader=%d) - allowing natural audio advancement",
                          motorPositionHoldoffFrames_.load(), faderActivityHoldoffFrames_.load());
                } else {
                    ALOGI("MOTOR_POSITION_DISABLED: Physics transitioning from %.4f to %.4f - allowing natural audio advancement",
                          currentPhysicsSpeed, targetPhysicsSpeed);
                }
            }
        }

        float effectivePlatterVol = platterVol;
        // Special handling for intro sound volume before first interaction
        if (activePlatterSample_->playOnceThenLoopSilently &&
            !activePlatterSample_->playedOnce &&
            !isFingerDownOnPlatter_.load() &&
            !activePlatterSample_->useEngineRateForPlayback_.load()
                ) {
            effectivePlatterVol = musicVol;
        }
        // DIAGNOSTIC: Capture state before rendering to detect unexpected jumps
        float startFrame = activePlatterSample_->preciseCurrentFrame.load();
        float rateSnapshot = platterTargetPlaybackRate_.load();
        bool engineRate = activePlatterSample_->useEngineRateForPlayback_.load();
        bool motorHoldoff = (motorPositionHoldoffFrames_.load() > 0);
        bool faderHoldoff = (faderActivityHoldoffFrames_.load() > 0);

        activePlatterSample_->getAudio(outputBuffer, numFrames, channelCount, effectivePlatterVol);

        // DIAGNOSTIC: Evaluate advancement vs expectation
        float endFrame = activePlatterSample_->preciseCurrentFrame.load();
        float totalFramesF = static_cast<float>(activePlatterSample_->totalFrames);
        float actualAdv = endFrame - startFrame;
        // Adjust for wrap-around if looping
        if (activePlatterSample_->loop.load() && totalFramesF > 0.0f) {
            if (actualAdv > totalFramesF * 0.5f) actualAdv -= totalFramesF;
            else if (actualAdv < -totalFramesF * 0.5f) actualAdv += totalFramesF;
        }
        float expectedAdv = rateSnapshot * numFrames; // approximate (snapshot semantics)
        // Flag large mismatches that could be perceived as jumps
        if (engineRate && std::fabs(actualAdv - expectedAdv) > std::max(50.0f, std::fabs(expectedAdv) * 2.0f)) {
            static int advLogCounter = 0;
            if (advLogCounter++ % 100 == 0) {
                ALOGE("ADV_MISMATCH: actual=%.2f, expected=%.2f, rate=%.4f, finger=%d, motorHold=%d, faderHold=%d, vol=%.2f",
                      actualAdv, expectedAdv, rateSnapshot, isFingerDown, motorHoldoff, faderHoldoff, effectivePlatterVol);
            }
        }
    }

    // CRITICAL DEBUG: Check if activeMusicSample_ is the same object as activePlatterSample_
    if (activeMusicSample_ && activeMusicSample_->isPlaying.load()) {
        if (activeMusicSample_.get() == activePlatterSample_.get()) {
            static int sameObjectLogCounter = 0;
            if (sameObjectLogCounter++ % 100 == 0) {
                ALOGE("DOUBLE GETAUDIO DETECTED: activeMusicSample_ and activePlatterSample_ are THE SAME OBJECT!");
                ALOGE("This causes DOUBLE position advancement - the root cause of 10x jumps!");
            }
            // DO NOT CALL getAudio again on the same object!
        } else {
            activeMusicSample_->getAudio(outputBuffer, numFrames, channelCount, musicVol);
        }
    }
    return oboe::DataCallbackResult::Continue;
}

void AudioEngine::onErrorBeforeClose(oboe::AudioStream *stream, oboe::Result error) { ALOGE("Oboe error before close: %s", oboe::convertToText(error)); }
void AudioEngine::onErrorAfterClose(oboe::AudioStream *stream, oboe::Result error) { ALOGE("Oboe error after close: %s", oboe::convertToText(error)); }

std::string AudioEngine::getPersistentCacheFilePath(const std::string& originalPathOrUri, uint64_t modificationTimestamp) {
    if (baseCachePath_.empty()) {
        ALOGE("AudioEngine: baseCachePath_ is not set. Cannot generate persistent cache file path.");
        return "";
    }

    std::string audioCacheSubDir = baseCachePath_ + "/audio_cache";
    struct stat st = {0};
    if (stat(audioCacheSubDir.c_str(), &st) == -1) {
        if (mkdir(audioCacheSubDir.c_str(), 0700) == -1 && errno != EEXIST) {
             ALOGE("AudioEngine: Failed to create cache subdirectory %s. Error: %s", audioCacheSubDir.c_str(), strerror(errno));
             return "";
        }
    }

    std::string cacheKeyString = originalPathOrUri + "_" + std::to_string(modificationTimestamp);
    std::string hashedName = computeSimpleHash(cacheKeyString);
    return audioCacheSubDir + "/" + hashedName + ".audiocache";
}

bool AudioEngine::isPlatterTouched() const { return isFingerDownOnPlatter_.load(); }
uint32_t AudioEngine::getStreamSampleRate() const { return streamSampleRate_; }

void AudioEngine::manageCache(std::map<std::string, std::shared_ptr<AudioSample>>& cache,
                            std::list<std::string>& lruList,
                            const std::string& key,
                            std::shared_ptr<AudioSample> sampleToCache) {
    lruList.remove(key);
    lruList.push_front(key);
    cache[key] = sampleToCache;

    if (cache.size() > MAX_CACHED_SAMPLES_PER_TYPE) {
        if (!lruList.empty()) {
            std::string lruKey = lruList.back();
            lruList.pop_back();
            cache.erase(lruKey);
            ALOGI("AudioEngine::manageCache (In-Memory): Evicted %s from in-memory cache.", lruKey.c_str());
        }
    }
}

void AudioEngine::updateLru(std::list<std::string>& lruList, const std::string& key) {
    lruList.remove(key);
    lruList.push_front(key);
}

// VinylTracker class definition - 360Hz audio-synchronized angle tracking
class VinylTracker {
private:
    std::atomic<bool> isTracking_{false};
    std::atomic<float> currentAngle_{0.0f};
    std::atomic<float> unwrappedAngle_{0.0f}; // CUMULATIVE TRACKING: Never resets, tracks total rotation
    std::atomic<float> totalRotations_{0.0f}; // NEW: Track number of full rotations
    pthread_t trackingThread_;
    std::atomic<bool> shouldStop_{false};
    bool threadCreated_{false};
    AudioEngine* audioEngine_;
    
    // ROTATION TRACKING: Variables for cumulative angle calculation
    std::atomic<uint64_t> lastTrackedTick_{0};
    std::atomic<bool> hasInitialTick_{false};
    
    static void* trackingLoopStatic(void* arg) {
        VinylTracker* tracker = static_cast<VinylTracker*>(arg);
        tracker->trackingLoop();
        return nullptr;
    }
    
    void trackingLoop() {
        ALOGI("VinylTracker: Tracking loop started with CUMULATIVE rotation tracking");
        
        // ULTRA-HIGH FREQUENCY TRACKING: Start at 2kHz and auto-optimize
        int64_t targetFrequencyHz = 2000; // Start at 2kHz (0.5ms intervals)
        int64_t frameTimeUs = 1000000 / targetFrequencyHz; // 500 microseconds
        
        // Performance monitoring for dynamic optimization
        auto lastPerformanceCheck = std::chrono::high_resolution_clock::now();
        int performanceCheckInterval = 10000; // Check every 10k iterations
        int iterationCounter = 0;
        int missedDeadlines = 0;
        
        ALOGI("VinylTracker: ULTRA-HIGH FREQUENCY MODE - Starting at %dHz (%.3fms intervals)", 
              (int)targetFrequencyHz, frameTimeUs / 1000.0);

        uint64_t lastMasterTick = 0;
        uint64_t baseTickOffset = 0; // Offset for when tracking starts
        bool firstIteration = true;

        while (!shouldStop_.load()) {
            auto loopStartTime = std::chrono::high_resolution_clock::now();
            
            if (isTracking_.load() && audioEngine_) {
                // PERFORMANCE MONITORING: Track timing accuracy
                iterationCounter++;
                
                // ULTRA-HIGH FREQUENCY DEBUG: Much less frequent logging for performance
                static int runningLogCounter = 0;
                if (runningLogCounter++ % 50000 == 0) { // Log every 50k iterations (~25 seconds at 2kHz)
                    ALOGI("VinylTracker: ULTRA-HIGH FREQ - %dHz, iteration %d", (int)targetFrequencyHz, runningLogCounter);
                }

                // Get master tick system for high-precision timing
                MasterTickSystem* masterTickSystem = audioEngine_->getMasterTickSystem();
                if (!masterTickSystem) {
                    usleep(frameTimeUs);
                    continue;
                }

                uint64_t currentMasterTick = masterTickSystem->getCurrentTick();

                // ===== UNIFIED SYNC: SIMPLE TICK-BASED TRACKING =====
                // Since DirectCallbackSync now resets master tick directly, we can use simple tracking
                if (currentMasterTick < lastMasterTick && !firstIteration) {
                    ALOGI("VinylTracker: DIRECT SYNC detected - Tick reset from %" PRIu64 " to %" PRIu64 "",
                          lastMasterTick, currentMasterTick);
                    
                    // CRITICAL FIX: Don't try to compensate for sync resets during large rotations
                    // The DirectCallbackSync is designed for small corrections, not cumulative tracking
                    // Just update our reference points without corrupting the cumulative count
                    lastMasterTick = currentMasterTick;
                    lastTrackedTick_.store(currentMasterTick);
                    
                    ALOGI("CUMULATIVE_TRACKING: Sync reset handled - preserving cumulative rotation count");
                }

                // CUMULATIVE INITIALIZATION: Set initial reference point
                if (firstIteration) {
                    ALOGI("VinylTracker: *** FIRST START *** Initializing cumulative rotation tracking");
                    lastMasterTick = currentMasterTick;
                    lastTrackedTick_.store(currentMasterTick);
                    hasInitialTick_.store(true);
                    
                    // Initialize angles based on current tick position
                    uint64_t ticksPerRotation = masterTickSystem->getTicksPerVinylRotation();
                    if (ticksPerRotation > 0) {
                        uint64_t tickInRotation = currentMasterTick % ticksPerRotation;
                        float initialAngle = (float)tickInRotation * 360.0f / (float)ticksPerRotation;
                        currentAngle_.store(initialAngle);
                        
                        // CRITICAL FIX: Don't reset cumulative tracking - preserve existing rotation count
                        float existingUnwrapped = unwrappedAngle_.load();
                        float existingRotations = totalRotations_.load();
                        
                        if (existingRotations == 0.0f && existingUnwrapped == 0.0f) {
                            // True first initialization
                            unwrappedAngle_.store(initialAngle);
                            totalRotations_.store(initialAngle / 360.0f);
                            ALOGI("CUMULATIVE_TRACKING: TRUE INIT - angle=%.2f°, rotations=%.3f", initialAngle, initialAngle / 360.0f);
                        } else {
                            // Resume from existing cumulative position
                            ALOGI("CUMULATIVE_TRACKING: RESUME INIT - preserving unwrapped=%.2f°, rotations=%.3f", existingUnwrapped, existingRotations);
                        }
                    }
                    firstIteration = false;
                }

                // ===== CUMULATIVE ANGLE CALCULATION: Track total rotation =====
                uint64_t ticksPerRotation = masterTickSystem->getTicksPerVinylRotation();
                if (ticksPerRotation > 0 && hasInitialTick_.load()) {
                    // Calculate wrapped angle (0-360°)
                    uint64_t tickInRotation = currentMasterTick % ticksPerRotation;
                    float wrappedAngle = (float)tickInRotation * 360.0f / (float)ticksPerRotation;
                    
                    // Calculate tick delta since last update
                    uint64_t lastTick = lastTrackedTick_.load();
                    int64_t tickDelta = (int64_t)currentMasterTick - (int64_t)lastTick;
                    
                    // Convert tick delta to angle delta
                    float angleDelta = (float)tickDelta * 360.0f / (float)ticksPerRotation;
                    
                    // Update cumulative unwrapped angle
                    float currentUnwrapped = unwrappedAngle_.load();
                    float newUnwrapped = currentUnwrapped + angleDelta;
                    
                    // Update rotation count
                    float rotations = newUnwrapped / 360.0f;
                    
                    // Store values
                    currentAngle_.store(wrappedAngle);
                    unwrappedAngle_.store(newUnwrapped);
                    totalRotations_.store(rotations);
                    lastTrackedTick_.store(currentMasterTick);
                    
                    // ULTRA-HIGH FREQUENCY OPTIMIZED: Drastically reduced logging for performance
                    static int logCounter = 0;
                    if (logCounter++ % 100000 == 0) { // Every 100k iterations (~50 seconds at 2kHz)
                        ALOGI("CUMULATIVE_TRACKING: Tick=%" PRIu64 ", Wrapped=%.1f°, Unwrapped=%.1f°, Rotations=%.2f", 
                              currentMasterTick, wrappedAngle, newUnwrapped, rotations);
                    }
                }

                lastMasterTick = currentMasterTick;
        } else {
            // Reset on tracking stop/start
            firstIteration = true;
        }
        
        // PERFORMANCE MONITORING & DYNAMIC OPTIMIZATION
        auto loopEndTime = std::chrono::high_resolution_clock::now();
        auto loopDuration = std::chrono::duration_cast<std::chrono::microseconds>(loopEndTime - loopStartTime);
        
        // Check if we're meeting our timing targets
        if (loopDuration.count() > frameTimeUs) {
            missedDeadlines++;
        }
        
        // DYNAMIC FREQUENCY OPTIMIZATION: Check performance every N iterations
        if (iterationCounter % performanceCheckInterval == 0) {
            auto now = std::chrono::high_resolution_clock::now();
            auto timeSinceLastCheck = std::chrono::duration_cast<std::chrono::milliseconds>(now - lastPerformanceCheck);
            
            float missedDeadlineRate = (float)missedDeadlines / (float)performanceCheckInterval;
            float avgLoopTime = (float)timeSinceLastCheck.count() / (float)performanceCheckInterval;
            
            ALOGI("PERFORMANCE: %dHz - %.1f%% missed deadlines, avg %.3fms/loop", 
                  (int)targetFrequencyHz, missedDeadlineRate * 100.0f, avgLoopTime);
            
            // ADAPTIVE FREQUENCY: Optimize based on performance
            if (missedDeadlineRate < 0.01f && targetFrequencyHz < 10000) {
                // <1% missed deadlines - try going faster!
                targetFrequencyHz = (int64_t)(targetFrequencyHz * 1.5f); // 50% increase
                frameTimeUs = 1000000 / targetFrequencyHz;
                ALOGI("FREQUENCY_UP: Increasing to %dHz (%.3fms intervals)", (int)targetFrequencyHz, frameTimeUs / 1000.0);
            } else if (missedDeadlineRate > 0.05f) {
                // >5% missed deadlines - slow down for stability
                targetFrequencyHz = (int64_t)(targetFrequencyHz * 0.8f); // 20% decrease
                frameTimeUs = 1000000 / targetFrequencyHz;
                ALOGI("FREQUENCY_DOWN: Decreasing to %dHz (%.3fms intervals) due to %.1f%% missed deadlines", 
                      (int)targetFrequencyHz, frameTimeUs / 1000.0, missedDeadlineRate * 100.0f);
            }
            
            // Reset counters
            missedDeadlines = 0;
            lastPerformanceCheck = now;
        }
        
        // ULTRA-HIGH FREQUENCY SLEEP: Adaptive timing
        usleep(frameTimeUs);
        }
        ALOGI("VinylTracker: Tracking loop stopped");
    }

public:
    VinylTracker(AudioEngine* engine) : threadCreated_(false), audioEngine_(engine) {
        ALOGI("VinylTracker: Constructor with audio sync");
    }
    
    ~VinylTracker() {
        ALOGI("VinylTracker: Destructor");
        stopTracking();
    }
    
    void startTracking() {
        if (isTracking_.load()) {
            ALOGW("VinylTracker: Already tracking");
            return;
        }

        shouldStop_.store(false);
        isTracking_.store(true);
        currentAngle_.store(0.0f);

        // CRITICAL FIX: Request precise sync from audio callback when starting
        if (audioEngine_) {
            audioEngine_->requestVinylSync();
            ALOGI("VinylTracker: Requested CallbackSync for precise synchronization");
        }

        int result = pthread_create(&trackingThread_, nullptr, trackingLoopStatic, this);
        if (result == 0) {
            threadCreated_ = true;
            ALOGI("VinylTracker: Started tracking with CallbackSync");
        } else {
            ALOGE("VinylTracker: Failed to create thread: %d", result);
            isTracking_.store(false);
        }
    }
    
    void stopTracking() {
        if (!isTracking_.load()) {
            return;
        }
        
        isTracking_.store(false);
        shouldStop_.store(true);
        
        if (threadCreated_) {
            pthread_join(trackingThread_, nullptr);
            threadCreated_ = false;
        }
        
        ALOGI("VinylTracker: Stopped tracking");
    }
    
    float getCurrentAngle() const {
        return currentAngle_.load();
    }
    
    float getUnwrappedAngle() const {
        return unwrappedAngle_.load();
    }
    
    // NEW: Get total number of rotations (can be fractional)
    float getTotalRotations() const {
        return totalRotations_.load();
    }
    
    // CUMULATIVE RESET: Reset cumulative tracking while preserving current position
    void resetAngle() {
        float currentWrapped = currentAngle_.load();
        unwrappedAngle_.store(currentWrapped); // Reset cumulative to current wrapped position
        totalRotations_.store(currentWrapped / 360.0f);
        lastTrackedTick_.store(0);
        hasInitialTick_.store(false);
        ALOGI("CUMULATIVE_TRACKING: Reset cumulative tracking - maintaining wrapped angle %.2f°", currentWrapped);
    }
    
    // AUTO-CALIBRATION: Set angle to specific value for sync correction
    void setAngle(float angle) {
        // Normalize angle to 0-360 range
        while (angle >= 360.0f) angle -= 360.0f;
        while (angle < 0.0f) angle += 360.0f;
        
        currentAngle_.store(angle);
        // CUMULATIVE FIX: Set unwrapped angle to maintain continuity
        unwrappedAngle_.store(angle);
        totalRotations_.store(angle / 360.0f);
        ALOGI("CUMULATIVE_TRACKING: Manual angle calibration to %.2f° (unwrapped reset)", angle);
    }
    
    bool isTracking() const {
        return isTracking_.load();
    }
};

// AudioEngine VinylTracker wrapper method implementations
void AudioEngine::startVinylTracking() {
    if (vinylTracker_) {
        vinylTracker_->startTracking();
    }
}

void AudioEngine::stopVinylTracking() {
    if (vinylTracker_) {
        vinylTracker_->stopTracking();
    }
}

float AudioEngine::getCurrentVinylAngle() {
    if (vinylTracker_) {
        return vinylTracker_->getCurrentAngle();
    }
    return 0.0f;
}

bool AudioEngine::isVinylTracking() {
    if (vinylTracker_) {
        return vinylTracker_->isTracking();
    }
    return false;
}

// Audio position methods for VinylTracker synchronization
float AudioEngine::getPlatterAudioPosition() {
    if (activePlatterSample_) {
        return activePlatterSample_->preciseCurrentFrame.load();
    }
    return 0.0f;
}

float AudioEngine::getPlatterTotalFrames() {
    if (activePlatterSample_) {
        return static_cast<float>(activePlatterSample_->totalFrames);
    }
    return 1.0f; // Avoid division by zero
}

float AudioEngine::getUnwrappedVinylAngle() {
    if (vinylTracker_) {
        return vinylTracker_->getUnwrappedAngle();
    }
    return 0.0f;
}

float AudioEngine::getTotalRotations() {
    if (vinylTracker_) {
        return vinylTracker_->getTotalRotations();
    }
    return 0.0f;
}

void AudioEngine::resetVinylAngle() {
    if (vinylTracker_) {
        vinylTracker_->resetAngle();
    }
}

// Master tick system methods
float AudioEngine::getCurrentTickBasedVinylAngle() const {
    uint64_t currentTick = masterTickSystem_.getCurrentTick();
    float tickBasedAngle = masterTickSystem_.ticksToVinylAngle(currentTick);

    // COMPREHENSIVE SYNC VERIFICATION SYSTEM
    // Verify complete chain: Audio → Tick → Angle → Visual
    // Run on every call since this method is only called periodically from Kotlin
    // Note: Now polled ~60 FPS by UI; throttle logs to avoid spam
    static std::atomic<uint64_t> s_logCounter{0};
    uint64_t c = ++s_logCounter;
    bool shouldLog = (c % 180 == 0); // roughly every 3 seconds at 60fps
    if (shouldLog) {
        ALOGI("SYNC_VERIFICATION_ACTIVE: getCurrentTickBasedVinylAngle() sampling");
    }
        
        // Get all angles in the chain
        float audioAngle = 0.0f;
        float vinylTrackerAngle = 0.0f;
        
        if (activePlatterSample_ && activePlatterSample_->totalFrames > 0) {
            // Calculate audio position angle
            float audioFrame = activePlatterSample_->preciseCurrentFrame.load();
            float audioProgress = audioFrame / (float)activePlatterSample_->totalFrames;
            audioAngle = audioProgress * 360.0f;
            
            // Normalize to 0-360 range
            while (audioAngle >= 360.0f) audioAngle -= 360.0f;
            while (audioAngle < 0.0f) audioAngle += 360.0f;
        }
        
        if (vinylTracker_) {
            vinylTrackerAngle = vinylTracker_->getCurrentAngle();
        }
        
        // Calculate angle differences (account for wrap-around)
        auto angleDiff = [](float a1, float a2) -> float {
            float diff = a1 - a2;
            while (diff > 180.0f) diff -= 360.0f;
            while (diff < -180.0f) diff += 360.0f;
            return abs(diff);
        };
        
        float audioToTickDiff = angleDiff(audioAngle, tickBasedAngle);
        float tickToVinylDiff = angleDiff(tickBasedAngle, vinylTrackerAngle);
        float audioToVinylDiff = angleDiff(audioAngle, vinylTrackerAngle);
        
        // SYNC VERIFICATION THRESHOLDS
        // ADAPTIVE THRESHOLDS: More tolerant during active platter interaction
        bool isActivelyTouched = isFingerDownOnPlatter_.load();
        const float MINOR_DRIFT_THRESHOLD = isActivelyTouched ? 5.0f : 2.0f;   // 5°/2° degrees
        const float MAJOR_DRIFT_THRESHOLD = isActivelyTouched ? 20.0f : 10.0f; // 20°/10° degrees
        
        bool hasMinorDrift = audioToTickDiff > MINOR_DRIFT_THRESHOLD || 
                            tickToVinylDiff > MINOR_DRIFT_THRESHOLD || 
                            audioToVinylDiff > MINOR_DRIFT_THRESHOLD;
                            
        bool hasMajorDrift = audioToTickDiff > MAJOR_DRIFT_THRESHOLD || 
                            tickToVinylDiff > MAJOR_DRIFT_THRESHOLD || 
                            audioToVinylDiff > MAJOR_DRIFT_THRESHOLD;
        
        if (hasMajorDrift && shouldLog) {
            ALOGE("SYNC_MAJOR_DRIFT: Audio=%.2f° Tick=%.2f° Vinyl=%.2f° | Diffs: A→T=%.2f° T→V=%.2f° A→V=%.2f°",
                  audioAngle, tickBasedAngle, vinylTrackerAngle,
                  audioToTickDiff, tickToVinylDiff, audioToVinylDiff);
                  
            // TEMPORARILY DISABLED: Auto-calibration while debugging unified sync
            ALOGI("AUTO_CALIBRATION: DISABLED for unified sync debugging - would reset %.2f° to %.2f°", 
                  vinylTrackerAngle, audioAngle);
            // if (vinylTracker_ && activePlatterSample_) {
            //     vinylTracker_->setAngle(audioAngle);
            // }
        } else if (hasMinorDrift && shouldLog) {
            ALOGW("SYNC_MINOR_DRIFT: Audio=%.2f° Tick=%.2f° Vinyl=%.2f° | Diffs: A→T=%.2f° T→V=%.2f° A→V=%.2f°",
                  audioAngle, tickBasedAngle, vinylTrackerAngle,
                  audioToTickDiff, tickToVinylDiff, audioToVinylDiff);
        
        // Log chain verification on every call (every 3 seconds from TickTest)
        uint64_t ticksPerRotation = masterTickSystem_.getTicksPerVinylRotation();
        uint32_t sampleRate = masterTickSystem_.getSampleRate();
        uint64_t currentTick = masterTickSystem_.getCurrentTick();
        
        if (activePlatterSample_ && shouldLog) {
            float audioFrame = activePlatterSample_->preciseCurrentFrame.load();
            ALOGI("SYNC_CHAIN_VERIFICATION: Frame=%.1f/%.0f (%.3f%%) → Tick=%" PRIu64 " → TickAngle=%.2f° → VinylAngle=%.2f°",
                  audioFrame, (float)activePlatterSample_->totalFrames, 
                  (audioFrame / (float)activePlatterSample_->totalFrames) * 100.0f,
                  currentTick, tickBasedAngle, vinylTrackerAngle);
        }
        if (shouldLog) {
            ALOGI("SYNC_SYSTEM_STATE: TicksPerRot=%" PRIu64 ", SampleRate=%u, CurrentTick=%" PRIu64,
                  ticksPerRotation, sampleRate, currentTick);
        }
    }

    return tickBasedAngle;
}

float AudioEngine::getCurrentAudioAngleDegrees() const {
    if (activePlatterSample_) {
        // Use a constant base of 115,200 frames per rotation (25 RPM at 48kHz)
        // This decouples visual/audio phase from sample length so short files don't spin faster
    constexpr double BASE_FRAMES_PER_ROTATION = 115200.0;
    double audioFrame = static_cast<double>(activePlatterSample_->preciseCurrentFrame.load());
    // Map current frame position into [0, BASE_FRAMES_PER_ROTATION)
    double modFrame = std::fmod(audioFrame, BASE_FRAMES_PER_ROTATION);
    if (modFrame < 0.0) modFrame += BASE_FRAMES_PER_ROTATION;
    double progress = modFrame / BASE_FRAMES_PER_ROTATION;
    float angle = static_cast<float>(progress * 360.0);
        return angle;
    }
    return 0.0f;
}

void AudioEngine::resetMasterTicks() {
    masterTickSystem_.reset();
    ALOGI("Master tick system reset");
}

// Helper method to setup tick system when platter sample changes
void AudioEngine::setupTicksPerVinylRotation() {
    // STEP 1: Enable tick system setup for VinylTracker only (audio playback still uses floats)
    if (activePlatterSample_ && activePlatterSample_->totalFrames > 0) {
        // One full sample = one full vinyl rotation
        uint64_t ticksPerRotation = static_cast<uint64_t>(activePlatterSample_->totalFrames);
        masterTickSystem_.setTicksPerVinylRotation(ticksPerRotation);
        ALOGI("MasterTickSystem: Set ticks per vinyl rotation to %" PRIu64 " (sample frames: %d)",
              ticksPerRotation, activePlatterSample_->totalFrames);
    } else {
        masterTickSystem_.setTicksPerVinylRotation(0);
        ALOGW("MasterTickSystem: No valid platter sample, ticks per rotation set to 0");
    }
}

// Callback-based synchronization method
void AudioEngine::requestVinylSync() {
    // Request sync from audio callback - this will be processed in the next audio callback
    // for perfect timing synchronization
    callbackSyncData_.needsSync.store(true);
    ALOGI("AudioEngine: Vinyl sync requested - will be processed in next audio callback");
}

// JNI public wrapper methods
void AudioEngine::loadAssetPlatterSample(const std::string& assetPath, uint64_t appVersionCode) {
    engineLoadAssetPlatter(assetPath, appVersionCode);
}

void AudioEngine::setScratchPlatterActive(bool active, float velocity) {
    scratchPlatterActiveInternal(active, velocity);
}

// JNI callback setup for track completion
void AudioEngine::setJavaCallback(JavaVM* jvm, jobject mainActivityObj) {
    javaVM_ = jvm;

    JNIEnv* env;
    if (jvm->GetEnv((void**)&env, JNI_VERSION_1_6) != JNI_OK) {
        ALOGE("AudioEngine: Failed to get JNI environment for callback setup");
        return;
    }

    // Create global reference to MainActivity object
    if (mainActivityGlobalRef_) {
        env->DeleteGlobalRef(mainActivityGlobalRef_);
    }
    mainActivityGlobalRef_ = env->NewGlobalRef(mainActivityObj);

    // Get the callback method ID
    jclass mainActivityClass = env->GetObjectClass(mainActivityObj);
    onTrackCompletedMethodID_ = env->GetMethodID(mainActivityClass, "onMusicTrackCompleted", "()V");

    if (!onTrackCompletedMethodID_) {
        ALOGE("AudioEngine: Failed to find onMusicTrackCompleted method");
        return;
    }

    ALOGI("AudioEngine: Java callback setup completed successfully");
}

// Helper method to notify Java when track completes
void AudioEngine::notifyTrackCompleted() {
    if (!javaVM_ || !mainActivityGlobalRef_ || !onTrackCompletedMethodID_) {
        ALOGW("AudioEngine: Cannot notify track completion - callback not properly set up");
        return;
    }

    JNIEnv* env;
    if (javaVM_->GetEnv((void**)&env, JNI_VERSION_1_6) != JNI_OK) {
        ALOGE("AudioEngine: Failed to get JNI environment for track completion callback");
        return;
    }

    // Call the Java callback method
    env->CallVoidMethod(mainActivityGlobalRef_, onTrackCompletedMethodID_);

    ALOGI("AudioEngine: Notified Java of music track completion");
}

// OPTIMIZED: Simplified slipmat parameter logging
void AudioEngine::setSlipmatDamping(float damping) {
    slipmatPhysics_.setDampingFactor(damping);
    // ALOGI("AudioEngine: SlipmatPhysics damping updated to %.4f", damping);
}

void AudioEngine::setSlipmatAbruptness(float abruptness) {
    slipmatPhysics_.setAbruptness(abruptness);
    // ALOGI("AudioEngine: SlipmatPhysics abruptness updated to %.4f", abruptness);
}

void AudioEngine::setSlipmatBaseFriction(float baseFriction) {
    slipmatPhysics_.setBaseFriction(baseFriction);
    // ALOGI("AudioEngine: SlipmatPhysics base friction updated to %.4f", baseFriction);
}

void AudioEngine::setSlipmatCurrentSpeed(float speed) {
    slipmatPhysics_.setCurrentSpeed(speed);
    ALOGI("AudioEngine: SlipmatPhysics current speed set to %.4f", speed);
}

float AudioEngine::getCurrentSlipmatSpeed() const {
    return slipmatPhysics_.getCurrentSpeed();
}

float AudioEngine::getTargetSlipmatSpeed() const {
    return slipmatPhysics_.getTargetSpeed();
}
