#pragma once
#include <string>
#include <vector>
#include <memory>
#include <atomic>
#include <android/asset_manager.h>

class AudioEngine; // Forward declaration

class AudioSample {
public:
    std::string filePath;
    std::vector<float> audioData;
    int32_t totalFrames = 0;
    int32_t channels = 0;
    uint32_t sampleRate = 0;
    std::atomic<bool> isPlaying{false};
    std::atomic<bool> loop{false};
    bool playOnceThenLoopSilently = false;
    bool playedOnce = false;
    std::atomic<float> preciseCurrentFrame{0.0f}; // Legacy floating-point position
    std::atomic<int64_t> currentTick{0}; // New tick-based position (integer precision)
    double doublePrecisionFrame{0.0}; // High-precision position for scratch samples
    AudioEngine* audioEnginePtr = nullptr;
    std::atomic<bool> useEngineRateForPlayback_{false};
    bool isMusicTrack = false; // Flag to identify if this is a music track (vs platter sample)

    static std::vector<std::vector<float>> sincTable;
    static bool sincTableInitialized;
    
    static const uint32_t PERSISTENT_CACHE_MAGIC_NUMBER = 0xDF5C047C;
    static const uint32_t PERSISTENT_CACHE_VERSION = 1;

    void load(AAssetManager* assetManager, const std::string& basePath, AudioEngine* engine, uint64_t modificationTimestamp);
    void loadFromMemory(const unsigned char* buffer, size_t bufferSize, const std::string& identifier, uint64_t modificationTimestamp);
    void getAudio(float* outputBuffer, int32_t numOutputFrames, int32_t outputStreamChannels, float effectiveVolume);

    // Tick-based position methods
    void setCurrentTick(int64_t tick) { currentTick.store(tick); }
    int64_t getCurrentTick() const { return currentTick.load(); }
    float tickToFramePosition(int64_t tick) const;
    int64_t framePositionToTick(float framePos) const;

private:
    void resampleDataTo(uint32_t targetSampleRate);
    void standardizeToVinylLength();
    bool saveToPersistentCache(const std::string& cacheFilePath, uint64_t modificationTimestamp);
    bool loadFromPersistentCache(const std::string& cacheFilePath, uint64_t expectedModificationTimestamp, const std::string& originalFilePathHint);
    
    static void precalculateSincTable();
    static double bessel_i0_approx(double x);
    static double kaiserWindow(double n_rel, double N_total_taps, double beta);
    bool hasExtension(const std::string& path, const std::string& extension);
    bool tryLoadPath(AAssetManager* assetManager, const std::string& currentPathToTry);

    inline float getSampleAt(int32_t frameIndex, int channelIndex) const {
        if (audioData.empty() || totalFrames == 0) return 0.0f;

        int32_t effectiveFrameIndex = frameIndex;
        if (loop.load()) {
            if (totalFrames > 0) {
                effectiveFrameIndex = frameIndex % totalFrames;
                if (effectiveFrameIndex < 0) {
                    effectiveFrameIndex += totalFrames;
                }
            } else {
                effectiveFrameIndex = 0;
            }
        } else {
            effectiveFrameIndex = std::max(0, std::min(frameIndex, totalFrames - 1));
        }

        size_t actualIndex = static_cast<size_t>(effectiveFrameIndex) * channels + (channelIndex % channels);
        if (actualIndex < audioData.size()) {
            return audioData[actualIndex];
        }
        return 0.0f;
    }
};
